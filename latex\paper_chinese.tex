ment}{docuendend{CJK}
\新可能性。

\协作优化开辟了中的隐私保护实应用隐私保护。该框架为各种现同时保持强.5\%，进17法，平均改优于最先进方-DLES显著验结果表明Fed
实。
及（4）全面的实验验证环境的成功扩展，以3）DLES到联邦合策略，（邦聚，（2）个性化联进率序列的轨迹相似性度量首个基于优化改献包括：（1）

我们的主要贡架。务优化框性的新型联邦多任索路径重构解决关键局限学习增强搜性化聚合和深度性驱动个S，一个通过轨迹相似ed-DLE
本文提出Fion}
usconcl\label{sec:{结论}

\section现出色平衡。
化性能之间实在隐私保护和优平衡：}Fed-DLEStbf{隐私-性能tex势。

\力提供实质性优化优解映射的能网络学习复杂适应度到度学习重构能力：}神经tbf{深。

\tex显著优于统一聚合方法：}定制全局模型化聚合收益\textbf{个性效。

相关性方面比静态特征更有测量任务性：}优化轨迹相似性在似性有效xtbf{轨迹相察}

\tesection{关键洞
\sub}
c:discussionabel{se{讨论}
\l
\section%的改进。
地优化实现23.2\架相比本加4.4\%，完整框%的改进，个性化聚合增似性贡献11.7\示轨迹相
结果显融研究}
tion{消

\subsec。$p < 0.01$）统计显著性（\%，具有17.5）平均改进法（FD-EMD的方现最佳，相比第二好始终表法中线方d-DLES在所有基性能比较}

Feection{整体ubsults}

\sl{sec:res
\labe析}ion{结果与分\sect-DLES

AFFBO、LocalFD-EMD、I基线方法：}FMTBO、\textbf{
d{itemize}
轮次：3
\en 学习率：0.3，训练em4D-D架构
\it-2D-m 神经网络：1$
\ite = 6{top}客户端：$K_Top-K相似.3$
\item 值：$\tau = 0\item 相似性阈itemize}
gin{
\bef{参数：}textbemize}

\发
\end{it\%探索，30\%开分配：70em 预算个任务一个）
\it8（每item 客户端数量：1估次数：110
\m 每个客户端最大评
\ite10$= D 度：$\item 维ize}
tem\begin{i
xtbf{配置：}

\ten{实验设置}\subsectioe}

\end{itemiz中/低）
（无交集 - 高/I-H/M/L\item N 高/中/低）
部分交集 -/L（H/M\item PI-- 高/中/低）
L（完全交集  CI-H/M/\item{itemize}
begin
\个任务组：题组织为九ES，这些问DL上评估Fed-同的多任务基准问题们在18个不{基准问题}

我ubsection\s
periments}
el{sec:exlab
\实验设计}on{
\secti困难。
息推断函数属性变得标函数特征，使得从共享信保护目：}通过轨迹抽象bf{函数隐私。

\text有优化结果问专直接共享，防止竞争对手访x}_k$不athbf{向量$\m：}特定解解隐私bf{护。

\text据的完全保保敏感优化数，确不离开客户端场所al{D}_k$从\mathc}原始数据集$extbf{数据隐私：证：

\t私保ed-DLES提供以下隐风险。

F模型反演攻击减少信息泄露定制的全局模型，通过聚合：}每个客户端接收 个性化{层次3 -
\textbf
式而非显式优化信息。学习模数据，这些参数表示享训练k$而非共\theta}_boldsymbol{数$\端交换神经网络参享：}客户- 模型参数共textbf{层次2 

\定解值或适应度幅度。不揭示特_k$，包含优化动态而athbf{r}进率序列$\mD}_k$被抽象为改thcal{数据$\ma始优化象：}原f{层次1 - 轨迹抽\textb信息抽象方法：

ES采用分层

Fed-DLion{信息抽象层次}subsect\sub持受保护。

同时保在实现有效知识共享的感优化信息保敏的隐私保护，确安全机制提供全面d-DLES通过多层析}

Fe隐私分ection{\subs探索。

进行彻底骤确保对最佳解的直接邻域变量采样。这个最终细化步解进行单止找到的最佳评估预算用于围绕迄今为3）：}剩余变量采样（阶段3.
\textbf{单
域。望区经网络识别的有希够高效开发神，能续空间中微调解特别有效化。CMA-ES在连MA-ES执行本地优从最佳预测解开始，采用C：}优化（阶段3.2）{CMA-ES

\textbf最优区域。构的搜索路径知识识别潜在。这利用重值来预测有希望的解适应度络略优于当前最佳的目标局模型用于通过查询网1）：}个性化全（阶段3.tbf{神经网络预测\tex

子阶段：三个合开发策略包含高质量解。

混可能错过的统方法发现传结合，发策略识与混合开间知构的搜索空优化。此阶段将重行最终其个性化全局模型执架的高潮，每个客户端利用段代表Fed-DLES框
联邦开发阶发}
联邦开段3：{阶subsection

\个性化全局模型。性，导致更鲁棒的任务相似性与模型可靠权机制平衡种双重加
这
虑其模型的可靠性。训练数据大小考j$的|$基于客户端${D}_jcal|\math据大小权重：}$xtbf{数大影响。

\te型具有更客户端对个性化全局模kj}$确保更相似的{相似性权重：}$S_{

\textbf合两个因素：重$w_{kj}$结构性挑战。

聚合权务优化中的异中受益来解决联邦多任相似任务保每个客户端主要从型。此方法通过确户端生成定制的全局模合以为每个客邦聚S}$，我们执行个性化联矩阵$\mathbf{算的相似性邦聚合}

基于计section{个性化联sub系数。

\sub$表示皮尔逊相关 \cdot)\cdot,text{corr}(列，$\j)]$是改进率序j(M_ldots, r_), \(1), r_j(2 [r_jathbf{r}_j =M_i)]$和$\mts, r_i(2), \ldor_i(, _i = [r_i(1)r}中$\mathbf{

其uation}
\end{eqmilarity}y_sijector{eq:tra
\labelthbf{r}_j)| \ma}_i,\mathbf{r}(rrxt{co\teS_{ij} = |on}
{equatigin
\be计算为：
之间的相似性和$j$。客户端$i$信息的本质动态而不揭示敏感量捕获优化过程相似性度量，该度新颖率的优化轨迹改进们提出基于计算}

我on{轨迹相似性subsecti

\sub集体智能。将个体优化经验转换为个性化模型聚合通过复杂的相似性计算和。此阶段隐私协作共享优化知识同时保护ES的核心创新，客户端-DLed性驱动聚合阶段代表F
轨迹相似动聚合}
段2：轨迹相似性驱tion{阶subsecn}

\d{equatioailed}
\en_rate_detmprovementbel{eq:i-1)|}
\last}(tk^{beest}(t)}{|f_{b - f_k^best}(t-1)_k^{rac{f(t) = \f
r_kn{equation}\begi对改进：
算每步的相改进率序列：}计extbf{为。

\t整体收敛行exp}}$，捕获{t=1}^{T_{est}(t)\}_{f_k^{b最佳适应度值的进展$\列：}记录最佳适应度序extbf{

\t态的丰富信息：包含关于优化动k$在探索期间收集}_mathcal{T数据$\率。

轨迹计算效全面的搜索空间覆盖与。探索过程旨在平衡系统数据收集势与，该策略结合进化采样的优探索策略执行本地优化$使用DLES客户端$k}

每个n{本地探索策略subsubsectio
\重要。
性计算的高质量数据集至关相似练和有意义实现有效神经网络训于构建能够享。此阶段对后续知识共地收集轨迹信息用于时系统地优化景观，同每个客户端独立探索其本础，d-DLES的基阶段作为Fe

分布式探索探索}1：分布式ubsection{阶段\s的负转移。

时防止来自不相似任务转移，同似任务间实现有效知识动的个性化聚合，它在相似性驱

关键创新在于轨迹相样。S和单变量采CMA-E络预测与结合神经网发策略执行最终优化，并使用混合开化全局模型，每个客户端接收其个性3 - 联邦开发：}\textbf{阶段型。

个性化全局模加权聚合为每个客户端生成神经网络参数的似性，通过相似客户端性。基于这些相序列以计算任务相似户端共享轨迹改进率性驱动聚合：}客轨迹相似f{阶段2 - 
\textb。
练的数据集经网络训适合神空间探索，同时构建此阶段确保全面的搜索息。优化，同时收集优化轨迹信探索策略执行本地独立使用DLES：}每个客户端布式探索- 分xtbf{阶段1 \te个顺序阶段：

含三DLES框架包d-。

Fe同时最大化协作优化收益严格隐私保护的保持框架运行，在ES通过精心设计的三阶段DL体框架}

Fed-n{整ubsectio

\s战。保护的关键挑识共享和隐私解决知动个性化聚合迹相似性驱新的轨过创Fed-DLES通任务优化环境。路径重构能力扩展到联邦多，它将DLES的强大搜索DLES）算法搜索（Fed-学习增强的联邦深度
本节介绍我们提出:method}
label{sec}
\：Fed-DLES方法n{提出的\sectio望区域。

向搜索空间的有希，将注意力导重要性应度值的解分配更高具有更好适$f_i$确保网络对度值。在损失函数中包含适应测，$f_i$是相应的}}_i$是网络预\mathbf{y，$\hat{示真实决策变量f{y}_i$表mathbn}

其中$\tio
\end{equaeq:fmse}el{)
\lab f_i +{y}}_i|^2\mathbf\hat{}_i - (|\mathbf{y{i=1}^{D} um_\s\frac{1}{D} }_{FMSE} = thcal{L
\man}gin{equatio
\be适应度质量的区域：
经网络专注于具有更好数，它引导神创新是FMSE损失函S的一个关键}

DLE函数差（FMSE）损失应度均方误ubsection{适ubs偏置向量。

\s重矩阵和的权层$i$表示hbf{b}_i$分别\mat$和$_i\mathbf{W}ign}

其中$t}
\end{aleq:outpu\label{bf{b}_3 }_2 + \maththbf{h\ma }_3athbf{W&= \my}} mathbf{hat{\\
\n2} \bel{eq:hidde) \laf{b}_2mathb \h}_1 +\mathbf{W}_2 (\mathbf{tanh= \2 &\mathbf{h}_den1} \\
label{eq:hidbf{b}_1) \+ \math}_1 f thbf{Wnh(\ma1 &= \tah}_
\mathbf{gn}in{ali\beg：

网络架构可数学表达为temize}

神经元
\end{i}$的$D$个{\mathbf{y}\hat产生决策变量预测$xtbf{输出层：}
\item \te活函数个隐藏层，使用双曲正切激经元的两$个神分别具有$2D$和$4Dtbf{隐藏层：}m \tex元
\ite$f$作为输入的单个神经接受适应度值tbf{输入层：}item \tex}
\emizegin{it\beD-D架构：

1-2D-4径重构设计的独特网络采用专门为搜索路S神经网络架构}

DLEbsection{神经\subsu过的高质量解。

可能错统进化方法息，导致发现传空间信的搜索发重构来开和单变量采样CMA-ES终阶段结合合开发：}最{阶段3 - 混extbf识。

\t构的连续知索空间结于搜优化信息转换为关离散段将点重构连续搜索路径。此阶映射，使得能够从离散采样变量的反向网络学习从适应度值到决策}专门设计的神经2 - 网络重构：{阶段xtbf\te

神经网络训练的数据集。解并构建适合阶段，算法收集高质量空间进行全面探索。在此搜索（UMDAc），对单变量边际分布算法连续别是优化的，特进化算子使用索采样：}算法开始 探xtbf{阶段1 -行：

\te阶段运三个顺序

DLES通过段优化策略}三阶on{subsecti破。

\sub，代表了进化优化的突为连续搜索空间知识息转换搜索路径并将离散采样信神经网络重构
DLES算法通过采用ES）}
强搜索（DLction{深度学习增

\subse。，影响优化过程信约束能力和通可能具有不同的计算客户端性：}{系统异构
\textbf偏离全局最优方向。
型更新地模，其中本导致客户端漂移遵循不同分布，l{D}_k$$\mathca异构性：}本地数据集extbf{统计

\t空间和最优区域。的搜索标函数，可能具有不同本不同的目化根务异构性：}不同客户端优textbf{任种形式：

\构性表现为多多任务优化中，数据异挑战}

在联邦{非IID数据tionbsec。

\subsu决这些局限性性化聚合策略来解战。我们的方法引入个用于联邦多任务优化面临挑，直接将FedAvg应IID数据分布于任务异构性和非
然而，由端的数据点总数。
k$是所有客户1}^{K} N_{k= = \sum_}

其中$Ntionua
\end{eqdavg}abel{eq:fe\l}
k^{(t){\theta}_\boldsymbolrac{N_k}{N} ^{K} \fum_{k=1}= \sta}^{(t+1)} {\theymboln}
\boldsatio\begin{equ）聚合规则是：

邦平均（FedAvg模型参数。标准联t$时客户端$k$的本地示通信轮次$k^{(t)}$表ol{\theta}_oldsymb知识。

设$\b享优化数而非原始数据共习原理通过模型参联邦学在优化的背景下，我们适应作模型训练。情况下实现协习在不集中数据的

联邦学tion{联邦聚合}subsubsec

\{联邦学习框架}bsection

\su交换信息。从不直接彼此进行模型聚合和协调，但中央服务器通信端定期与器架构，其中客户模型遵循客户端-服务
通信
防止推理攻击。不应直接暴露以隐私：}中间和最终解{解\textbf
示。
能被揭式不息，其显式形)$是每个客户端的专有信\cdot：}目标函数$f_k(bf{函数隐私。

\text务器其他客户端或中央服，不能传输给须保留在客户端$k$上D}_k$必al{mathc私：}原始优化数据$\textbf{数据隐
\的隐私约束：
严格
联邦设置施加信模型}
{隐私约束和通ionubsect

\subs户端间直接共享。_k$不能在客D}hcal{at，原始数据集$\m于隐私要求相应的目标值。由)$是x}_k^{(i)}_k(\mathbf{} = fk^{(i)变量向量，$y_个决策$i$mega_k$表示第\O\in k^{(i)} mathbf{x}_适应度对，其中$\的解-$N_k$个已评估}^{N_k}$，包含}_{i=1)})\y_k^{(i, }_k^{(i)}thbf{xma \{(\ =mathcal{D}_k一个本地数据集$\个客户端维护}

每end{itemize任务的最优解
\端$k$优化f{x}_k^*$是客户m $\mathb索空间
\itek$的$D$维搜D$是任务$mathbb{R}^q \etea_k \subsitem $\Omeg贵黑盒目标函数
\$k$的昂hbb{R}$是客户端row \mathtarrig\k _k: \Omega_m $f}
\iteemize\begin{it
其中：
}
{equationend
\n}imizatiot_optl{eq:clien
\labex})k(\mathbf{\Omega_k} f_} \in _{\mathbf{x \arg\minbf{x}_k^* =\math{equation}


\begin下优化问题：$k$旨在解决以。

形式上，客户端时同时解决多个优化问题护客户端间数据隐私的同化任务相关联。目标是在保个不同的优 K\}$与一dots,, \l\in \{1, 2$k 户端其中每个客系统，的分布式有$K$个客户端考虑一个具在联邦多任务优化中，我们}

{联邦多任务优化ectionbsubs表述}

\suion{问题ct\subse强搜索算法。

和深度学习增题表述、联邦学习原理，包括问关键概念ES框架的数学基础和Fed-DL我们提出的本节建立}

esminarielic:prlabel{se
\on{预备知识}\secti

之间实现最优平衡。化性能护和优决这些空白，在隐私保LES扩展到联邦环境来解性化聚合并将D动的个入轨迹相似性驱DLES通过引ed-我们提出的F但存在隐私漏洞。

性能良好限，要么实现隐私保护但性能收益有供强要么提私-性能权衡：}现有方法f{隐
\textb动态。
迹的丰富捕获优化轨BO中的预测排名），无法的相似性度量（如FMT}当前方法使用简单似性度量：分的相extbf{不充。

\t强大搜索路径重构能力集中式设置中展示的ES在用DL没有现有的联邦优化方法利：}缺乏深度学习集成

\textbf{性。早期收敛和模型特定局限存在然更有效，但FD-EMD虽信息。的有限动态关于优化数共享，这携带这样的现有方法依赖超参FMTBO识共享：}像化中的有限知务优\textbf{联邦多任
键空白：
了激发我们工作的几个关揭示文献回顾机}

tion{研究空白与动\subsec到分布式环境。

径重构机制适应的同时，将搜索路户端间实现有效知识共享具有不同优化任务的客保护数据隐私并在空白。挑战在于在了一个重要的研究设置，代表尚未扩展到联邦机环境中有效，DLES
尽管在单化性能。
方法显著改进优的区域，相比传统均方误差好适应度质量注于具有更函数引导网络专。FMSE损失搜索路径发现高质量解够沿重构的相应的决策变量，使得能并预测作为输入经网络以适应度值反向映射方法，其中神于其的关键创新在

DLES开发。-ES和单变量采样的混合重构，以及结合CMA函数的神经网络E）损失方误差（FMS具有适应度均索采样、UMDAc算子的探算法分三个阶段运行：使用索空间知识。该样信息转换为连续搜网络，将离散采架构的全连接神经具有1-2D-4D-D采用独特设计的LES数的新范式。D而非近似目标函用神经网络重构搜索路径S）算法，它引入了使习增强搜索（DLE提出的深度学个突破是Song等人该领域的一非搜索空间重构。

常专注于函数近似而而，这些方法通函数的计算高效近似。然贵目标方向，其中神经网络作为昂辅助进化算法代表一个突出代理辅助工具。作为改进进化搜索的更相关，采用神经网络类与我们的工作果。

第二了最先进的结动神经架构设计中取得在自（ENAS）构搜索化神经架方法如进期的数方面表现出成功。更近网络结构和参法在进化变体等方权重。诸如NEAT及其络拓扑和优化神经网于神经进化，其中进化算法。

第一类专注搜索过程进化络增强以及采用神经网架构和参数，进化算法优化神经网络分为两种主要方法：使用可大致。这种集成了显著关注增强优化性能的手段获得过学习表示和自适应机制习与进化算法的集成作为通深度学进化算法}

增强ction{深度学习

\subse的特定模型架构。限制其通用性优化性能，要么依赖么为隐私保护牺牲现有方法要效的全局代理模型。情况下构建有式训练数据的访问分布在于在没有直接
联邦优化的基本挑战设计的特定获取函数。
赖为RBFN模型敛问题并依存在早期收出改进的性能，但。尽管FD-EMD表现代理模型的集成模型蒸馏用基于RBFNFD-EMD，采n提出了Wang和Ji了知识转移有效性。参数的有限信息内容限制BO提供强隐私保护，但超态聚合。虽然FMT量进行动预测排名的相似性度过程超参数，并使用基于间共享高斯O），仅在客户端叶斯优化（FMTB等人开发了联邦多任务贝被提出用于此场景。Zhu种方法能相关的任务。仅有两及多个客户端优化不同但可的设置涉挑战性邦多任务优化：}这个最具tbf{联。

\tex隐私，限制了其实际适用性过程中新采样数据的优化D-EA无法保护机制。然而，FD强隐私保护私技术增使用差分隐展到昂贵的多目标问题，并。该方法被扩合它们以形成全局代理模型并使用排序平均聚络（RBFN），端构建本地径向基函数网个客户（FDD-EA），在每入了联邦数据驱动进化算法同的目标函数。Xu等人引器协调机制协作优化相使用中央服务此设置中，多个客户端联邦单任务优化：}在textbf{\私。

时保护用户级隐略，在保持优化性能的同隐私和分布式探索策S-DE结合差分DP-FT协作中运行。隐私增强变体联邦习范式而非真正的现知识转移，但在转移学方法通过参数共享实）。该汤普森采样（FTS过程的联邦近似参数化高斯特征于随机傅里叶i等人提出了基的场景。Da知识优化特定任务前解决任务的用其他客户端先置考虑目标客户端使}此设邦转移优化：{联\textbf置。

要设系分为三个主标和客户端关新兴领域可根据优化目保护数据隐私。这个优化问题，同时分布式客户端间昂贵黑盒到解决邦优化将联邦学习原理扩展

联ion{联邦优化}ect求。

\subs违反联邦设置中的隐私要共享，将景观机制，如解交换或适应度知识转移数据。直接应用约束的分布式无法处理具有隐私限性在于优化方法的局多任务感场景。

现有其不适合隐私敏数据的无限制访问，使根本上假设对所有任务些集中式方法从，这性。然而方面表现出有效化经验在利用历史优算法等方法化和自适应转移进化优化问题。诸如序列转移优显式转移到新的解决任务的知识被，其中来自先前法构成另一个重要方向基于转移学习的方

扩展。多目标问题的各种领域特定，以及针对约束和移机制的MFEA-II结合自适应转。后续发展包括移，开创了这种方法叉操作实现隐式知识转不同任务个体间的交和标量适应度度量，通过通过引入技能因子子进化算法（MFEA）。多因任务多个种群并发优化使用单一分支，其中代表了一个突出优化（EMTO）
进化多任务来加速收敛。
专注于利用任务相似性式数据访问，该领域的传统方法假设集中问题的强大范式。个相关优化移机制同时解决多识转已成为通过利用知}

多任务优化优化ion{多任务bsect

\su作的研究空白。，突出激发我们工强进化算法深度学习增邦优化和：多任务优化、联个关键领域的相关文献

本节回顾三ed}lat{sec:re
\labeltion{相关工作}}

\secateer
\end{enum能改进。比最先进方法的显著性了相大量实验，证明任务上进行了8个不同的基准实验验证：}我们在1\textbf{全面的item 架。

\发阶段的系统优化框索、神经网络重构和混合开分布式探架：}我们设计了包含三阶段框{完整的textbf \略。

\item个性化聚合策客户端生成定制全局模型的发了基于轨迹相似性为每个：}我们开联邦聚合bf{个性化m \text\ite能力。

用强大的非线性拟合为搜索路径重构启邦环境，算法扩展到联LES重构：}我们成功将D深度学习增强搜索em \textbf{。

\it迹相似性度量系数的优化改进率序列的轨首个基于使用皮尔逊相关聚合：}我们引入了相似性驱动 \textbf{轨迹teme}
\iin{enumerat：

\beg以下关键贡献度学习增强搜索），做出-DLES（联邦深}

我们提出Fedn{我们的贡献
\subsectio搜索空间。
无法重构连续获排序信息而对排名关系，但只能捕学习成类器FFBO}使用神经网络分textbf{IA获取函数。

\并依赖特定的早期收敛问题型蒸馏，但存在集成模RBFN代理模型的{FD-EMD}采用基于tbf
\tex简单。
名的相似性度量过于信息。基于预测排带关于底层优化动态的有限，这些参数携高斯过程超参数FMTBO}仅共享bf{xt

\te现出几个关键局限性：当前最先进的方法表

局限性}tion{现有方法的

\subsec获丰富的优化动态。型参数，这可能无法完全捕限信息，如超参数或模享有赖于共利用：}现有方法通常依信息f{有限的\textb
要仔细平衡。
识共享需效的知严格隐私保护的同时实现有-性能权衡：}在保持bf{隐私

\text偏离。局最优方向大幅地模型更新与全显著的客户端漂移，其中本性可能导致多样务间优化景观的同任客户端漂移：}不textbf{的优化任务。

\涉及客户端间根本不同同，联邦多任务优化不同数据分布不在相同任务上处理端通常习中客户}与联邦学据分布：{非IID数
\textbf基本挑战：

联邦多任务优化面临几个中的挑战}
邦多任务优化ction{联。

\subse协作收益护数据隐私的同时最大化矛盾：在保多任务优化中的核心这体现了联邦禁止直接的数据交换。疗数据的敏感性与方，但医作可以显著惠及所有参的协习模型。虽然通过知识共享疗诊断的机器学医院希望优化各自用于医际场景，多家战。

考虑一个实法带来了前所未有的挑任务优化方统的集中式多长的重视为传日益增据隐私和安全然而，对数加速收敛并提高解的质量。关任务间的共享知识来本前提在于利用相优化。其基优到分布式系统中的协作从不同机构间的超参数调范式，实应用中的关键成为众多现
多任务优化已on{背景与动机}
ti

\subsecroduction}bel{sec:int}
\laction{引言

\serds}{IEEEkeywo\end私保护
似性，隐习，轨迹相深度学优化，多任务优化，
联邦keywords}\begin{IEEEtract}

\end{abs大的隐私保护。
保持强D数据分布下，同时，特别是在非II进的方法于最先Fed-DLES显著优实验表明，上的大量框架。在18个基准任务整三阶段优化网络重构和混合开发的完神经分布式探索、，以及（3）包含性化联邦聚合策略制全局模型的个（2）为每个客户端生成定度量，似性改进率序列的轨迹相逊相关系数的优化基于使用皮尔创新：（1）引入了三个关键的方法任务优化框架。我们的新型联邦多化聚合相结合个性迹相似性驱动的索（DLES）与轨S，一个将深度学习增强搜ed-DLE些挑战，我们提出了F性。为解决这搜索空间等局限无法重构连续期收敛以及足、早FFBO存在信息利用不O、FD-EMD和IA挑战。现有方法如FMTB临重大数据方面面on-IID）同分布（n处理不同客户端间非独立保护的同时衡知识共享和隐私}
联邦多任务优化在平ractstegin{ab

\bketitlema

\ty.edu}
}@universi\
email城市，国家 \大学名称\\
\
{计算机科学学院\thorblockAEEEau
\IlockN{作者姓名}horbIEEEaut{\author化}

\务优邦多任强搜索路径重构的联tle{基于深度学习增sn}

\ti8}{gbCJK}{UTFn{gi
\bement}ocu\begin{d8}

JKutfkage{Cpac
\usee{subfigure}epackags}
\uskage{booktabw}
\usepackage{multirousepac\de}
udoco{algpsekageac}
\usepge{algorithm
\usepackaage{xcolor}epackcomp}
\usackage{textsep
\uaphicx}ckage{grc}
\usepahmige{algoritsepackats}
\ussymb,amsfonmsmath,amage{a\usepack
{cite}\usepackage
dlockoutserridecommann}
\IEEEovtraence]{IEEEass[confer\documentcl