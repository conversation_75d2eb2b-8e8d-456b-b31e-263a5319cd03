#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行联邦多任务优化系统
每个客户端优化18个任务中的一个，服务器聚合知识
"""

import numpy as np
import time
import json
import os
import argparse
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import List
from federated_dles import FederatedDLESClient, FederatedDLESServer
from Tasks.benchmark import create_tasks_diff_func

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='联邦多任务优化')

    # 数据划分相关参数
    parser.add_argument('--iid', default=False, action='store_true',
                       help='client dataset partition methods')
    parser.add_argument('--np_per_dim', default=2, type=int,
                       help='number of partitions per feature dimension')

    # 实验控制参数
    parser.add_argument('--num_runs', default=20, type=int,
                       help='number of runs')
    parser.add_argument('--multi_task', default=True, action='store_true',
                       help='if using multi-task optimization')
    parser.add_argument('--save_results', default=True, action='store_true',
                       help='if saving the results')

    # 基于轨迹相似性的智能聚合参数
    parser.add_argument('--similarity_threshold', default=0.3, type=float,
                       help='similarity threshold for trajectory-based aggregation (0.05-0.3, recommended: 0.1)')
    parser.add_argument('--top_k_similar', default=6, type=int,
                       help='number of most similar clients to aggregate with (1-8, recommended: 3-5)')

    # 其他参数
    parser.add_argument('--dim', default=10, type=int, help='problem dimension')
    parser.add_argument('--max_evaluations', default=110, type=int,
                       help='maximum evaluations per client')
    parser.add_argument('--population_size', default=21, type=int,
                       help='initial population size for optimization')
    parser.add_argument('--func', default='mixed', type=str,
                       help='function type for single task')
    parser.add_argument('--acq_type', default='dles', type=str,
                       help='acquisition type')
    parser.add_argument('--gamma', default=1.0, type=float,
                       help='gamma parameter')

    return parser.parse_args()


def create_clients(tasks: List, task_names: List[str], dimension: int = 10,
                  max_evaluations: int = 110, iid: bool = False,
                  np_per_dim: int = 2) -> List[FederatedDLESClient]:
    """
    创建客户端列表
    Args:
        tasks: 任务列表
        task_names: 任务名称列表
        dimension: 问题维度
        max_evaluations: 最大评估次数
    Returns:
        客户端列表
    """
    clients = []
    for i, (task, task_name) in enumerate(zip(tasks, task_names)):
        client = FederatedDLESClient(
            client_id=i,
            task=task,
            task_name=task_name,
            dimension=dimension,
            max_evaluations=max_evaluations,
            iid=iid,
            np_per_dim=np_per_dim
        )
        clients.append(client)

    return clients


def run_single_federated_optimization(args, run_id: int):
    """
    运行单次联邦多任务优化
    Args:
        args: 命令行参数
        run_id: 运行ID
    Returns:
        客户端结果和时间信息
    """
    print(f"=" * 60)
    print(f"运行 {run_id + 1}/{args.num_runs} - 联邦多任务优化")
    print(f"=" * 60)

    # 创建18个任务 - 每次运行使用不同的种子
    dimension = args.dim
    max_evaluations = args.max_evaluations
    # 为每次运行生成不同的种子
    run_seed = int(time.time() * 1000 + run_id * 1000) % 2**32
    # 同时设置numpy、random和torch的种子，确保每次运行的随机性不同
    np.random.seed(run_seed)
    import random
    random.seed(run_seed)
    # 设置PyTorch随机种子
    import torch
    torch.manual_seed(run_seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(run_seed)
        torch.cuda.manual_seed_all(run_seed)
    tasks = create_tasks_diff_func(dimension, normalized=False, seed=run_seed)

    
    task_names = [
        "CI-H Task1 (Griewank)", "CI-H Task2 (Rastrigin)",
        "CI-M Task1 (Ackley)", "CI-M Task2 (Rastrigin)", 
        "CI-L Task1 (Ackley)", "CI-L Task2 (Schwefel)",
        "PI-H Task1 (Rastrigin)", "PI-H Task2 (Sphere)",
        "PI-M Task1 (Ackley)", "PI-M Task2 (Rosenbrock)",
        "PI-L Task1 (Ackley)", "PI-L Task2 (Weierstrass)",
        "NI-H Task1 (Rosenbrock)", "NI-H Task2 (Rastrigin)",
        "NI-M Task1 (Griewank)", "NI-M Task2 (Weierstrass)",
        "NI-L Task1 (Rastrigin)", "NI-L Task2 (Schwefel)"
    ]
    
    # 创建客户端和服务器
    clients = create_clients(tasks, task_names, dimension, max_evaluations,
                           args.iid, args.np_per_dim)
    server = FederatedDLESServer(dimension)

    total_clients = len(clients)

    print(f"创建了 {total_clients} 个客户端和 1 个服务器")
    print(f"数据划分方式: {'IID' if args.iid else f'Non-IID (np_per_dim={args.np_per_dim})'}")
    print(f"将使用基于轨迹相似性的聚合策略")
    
    # 第一阶段：客户端本地探索
    print("\n" + "=" * 60)
    print("第一阶段：客户端本地探索")
    print("=" * 60)
    
    start_time = time.time()
    exploration_results = []
    
    for client in clients:
        result = client.local_exploration()
        exploration_results.append(result)
    
    exploration_time = time.time() - start_time
    
    # 服务器聚合探索结果
    global_exploration_stats = server.aggregate_exploration_results(exploration_results)
    
    # 第二阶段：客户端本地模型训练
    print("\n" + "=" * 50)
    print("第二阶段：客户端本地模型训练")
    print("=" * 50)

    training_start_time = time.time()
    client_models = []

    # 所有客户端都训练自己的神经网络，所有模型都参与后续的智能聚合
    for client in clients:
        model_params = client.train_local_model()
        if model_params is not None:
            client_models.append(model_params)

    print(f"所有 {len(client_models)} 个客户端完成神经网络训练，将基于轨迹相似性进行智能聚合")

    training_time = time.time() - training_start_time

    # 第三阶段：基于轨迹相似性的智能联邦聚合
    print("\n" + "=" * 50)
    print("第三阶段：基于轨迹相似性的智能联邦聚合")
    print("=" * 50)

    aggregation_start_time = time.time()

    # 始终使用基于轨迹相似性的个性化聚合
    personalized_models = server.personalized_federated_averaging(
        client_models,
        similarity_threshold=args.similarity_threshold,
        top_k_similar=args.top_k_similar
    )
    global_model_state = personalized_models  # 字典，包含每个客户端的个性化模型

    aggregation_time = time.time() - aggregation_start_time
    
    # 第四阶段：客户端更新全局模型并进行联邦开发
    print("\n" + "=" * 60)
    print("第四阶段：联邦开发")
    print("=" * 60)
    
    exploitation_start_time = time.time()
    
    # 分发基于轨迹相似性的个性化模型给客户端
    for client in clients:
        if client.client_id in global_model_state:
            client.update_global_model(global_model_state[client.client_id])
            print(f"客户端 {client.client_id} 接收基于轨迹相似性的个性化模型")
        else:
            # 如果某个客户端没有个性化模型，使用传统联邦平均作为备选
            fallback_global_state = server.federated_averaging(client_models)
            client.update_global_model(fallback_global_state)
            print(f"客户端 {client.client_id} 接收备选全局模型（无个性化模型）")
    
    # 客户端使用全局模型进行开发
    final_results = []
    for client in clients:
        result = client.federated_exploitation()
        final_results.append(result)
    
    exploitation_time = time.time() - exploitation_start_time
    
    # 服务器聚合最终结果
    final_stats = server.aggregate_final_results(final_results)
    
    total_time = time.time() - start_time

    # 计算每轮时间（这里只有一轮）
    time_per_round = [total_time]

    # 返回结果
    return {
        'clients': clients,
        'final_results': final_results,
        'final_stats': final_stats,
        'time_per_round': time_per_round,
        'total_time': total_time,
        'run_id': run_id
    }

def save_experiment_results(args, all_run_results):
    """保存实验结果"""
    if not args.save_results:
        return

    # 确定数据分布类型
    if args.iid:
        data_distribution = 'IID'
    else:
        data_distribution = f'NIID{args.np_per_dim}'

    # 创建动态保存目录 - 包含种群大小和评估次数信息
    root_save_file = f'results_dles_sim{args.similarity_threshold}_init{args.population_size}_fe{args.max_evaluations}'
    if args.multi_task:
        root_save_file += '_mt'
        save_dir = os.path.join(root_save_file, data_distribution)
    else:
        save_dir = os.path.join(root_save_file, args.func, data_distribution)

    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 为每次运行保存结果
    for run_result in all_run_results:
        run_id = run_result['run_id']
        clients = run_result['clients']
        time_per_round = run_result['time_per_round']

        # 构建简化的文件名
        # 格式: run{id}_{task_type}_{algorithm}.csv
        # 例如: run0_MT_DLES.csv 或 run0_ST_DLES.csv
        file_name = f'run{run_id}'

        # 任务类型标识
        if args.multi_task:
            file_name += '_MT'  # Multi-Task
        else:
            file_name += '_ST'  # Single-Task

        # 算法标识
        file_name += f'_{args.acq_type.upper()}'

        # 添加文件扩展名
        file_name += '.csv'
        record_file_name = os.path.join(save_dir, file_name)

        # 准备数据 - 确保所有数组长度相同
        max_length = max(len(c.fitness_values) for c in clients)

        record_data = {}
        for c in clients:
            # 填充到相同长度
            fitness_values = c.fitness_values.copy()
            while len(fitness_values) < max_length:
                fitness_values.append(np.nan)  # 用NaN填充
            record_data[f'client{c.client_id}'] = fitness_values

        # 时间数据也需要填充到相同长度
        time_values = time_per_round.copy()
        while len(time_values) < max_length:
            time_values.append(np.nan)
        record_data['time'] = time_values

        # 保存为CSV
        record_pd = pd.DataFrame(data=record_data)
        record_pd.to_csv(record_file_name, index=True)

        print(f"运行 {run_id} 结果已保存到: {record_file_name}")


def auto_analyse_results(args):
    """
    自动调用analyse.py分析实验结果
    Args:
        args: 命令行参数
    """
    try:
        import subprocess
        import sys

        # 确定数据分布类型
        if args.iid:
            data_distribution = 'IID'
        else:
            data_distribution = f'NIID{args.np_per_dim}'

        # 构建动态结果目录路径 - 包含种群大小和评估次数信息
        # 注意：这里使用sim而不是ktp，因为当前系统使用相似性阈值
        root_save_file = f'results_dles_sim{args.similarity_threshold}_init{args.population_size}_fe{args.max_evaluations}'
        if args.multi_task:
            root_save_file += '_mt'
            result_dir = os.path.join(root_save_file, data_distribution)
        else:
            result_dir = os.path.join(root_save_file, args.func, data_distribution)

        # 获取绝对路径
        result_dir_abs = os.path.abspath(result_dir)

        print(f"\n{'='*60}")
        print("开始自动分析实验结果...")
        print(f"结果目录: {result_dir_abs}")
        print(f"{'='*60}")

        # 检查目录是否存在
        if not os.path.exists(result_dir_abs):
            print(f"警告: 结果目录不存在: {result_dir_abs}")
            return

        # 调用analyse.py脚本
        analyse_script = os.path.join(os.getcwd(), 'utils', 'analyse.py')
        if not os.path.exists(analyse_script):
            print(f"警告: analyse.py脚本不存在: {analyse_script}")
            return

        # 简化的输出文件名：analysis_{数据分布}.csv
        output_filename = f"analysis_{data_distribution}.csv"
        print(f"分析结果文件名: {output_filename}")

        # 使用subprocess调用analyse.py，使用--dirs和--output参数
        cmd = [sys.executable, analyse_script, '--dirs', result_dir_abs, '--output', output_filename]
        print(f"执行命令: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())

        if result.returncode == 0:
            print("✅ 结果分析完成!")
            if result.stdout:
                print("分析输出:")
                print(result.stdout)
        else:
            print("❌ 结果分析失败!")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)

    except Exception as e:
        print(f"自动分析结果时发生错误: {e}")
        import traceback
        traceback.print_exc()


def run_multiple_experiments(args):
    """运行多次实验"""
    print("=" * 80)
    print("联邦多任务优化系统 - 多次运行实验")
    print("=" * 80)
    print(f"实验配置:")
    print(f"  运行次数: {args.num_runs}")
    print(f"  问题维度: {args.dim}")
    print(f"  最大评估次数: {args.max_evaluations}")
    print(f"  数据划分: {'IID' if args.iid else f'Non-IID (np_per_dim={args.np_per_dim})'}")
    print(f"  相似性阈值: {args.similarity_threshold}")
    print(f"  聚合伙伴数: {args.top_k_similar}")
    print("=" * 80)

    all_run_results = []
    all_best_fitness = []

    for run_id in range(args.num_runs):
        print(f"\n开始运行 {run_id + 1}/{args.num_runs}")

        try:
            run_result = run_single_federated_optimization(args, run_id)
            all_run_results.append(run_result)

            # 收集最优适应度
            run_best_fitness = [r['final_best_fitness'] for r in run_result['final_results']]
            all_best_fitness.extend(run_best_fitness)

            print(f"运行 {run_id + 1} 完成，全局最优适应度: {min(run_best_fitness):.6e}")

        except Exception as e:
            print(f"运行 {run_id + 1} 失败: {e}")
            continue

    # 保存结果
    if args.save_results:
        save_experiment_results(args, all_run_results)

    # 统计结果
    print("\n" + "=" * 80)
    print("多次运行统计结果")
    print("=" * 80)
    print(f"成功运行次数: {len(all_run_results)}/{args.num_runs}")
    if all_best_fitness:
        print(f"全局最优适应度: {min(all_best_fitness):.6e}")
        print(f"平均最优适应度: {np.mean(all_best_fitness):.6e}")
        print(f"适应度标准差: {np.std(all_best_fitness):.6e}")

    # 自动调用分析脚本
    if args.save_results and len(all_run_results) > 0:
        auto_analyse_results(args)

    return all_run_results


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        print("启动联邦多任务优化系统...")
        all_results = run_multiple_experiments(args)

        print("\n" + "=" * 80)
        print("联邦多任务优化实验完成！")
        print("=" * 80)

        return all_results

    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
