#!usr/bin/env/ python
# -*- coding:utf-8 -*-
"""
Author:<PERSON><PERSON><PERSON>
Date:2024-04-12
"""
from utils.constants import ArgChoices
from utils.result_analysis import FileProcessor
from tqdm import tqdm

if __name__ == '__main__':
    root = 'results'
    alg_list = ['FMTBO', 'EFMO_CM', 'EFMO_CR', 'EFMO_SR', 'EFMO']
    temp_alg_list = ['EMATOMKT', 'DPFTSDE']
    pb_list = (1, 2, 4, 6)
    SAVE = True
    STD_INFO = False
    LOG_VAL = False
    STYLE_ID = 0
    THRESHOLD = 0.05
    SUFFIX = '.png'

    result_analysis = FileProcessor(
        root=root,
        alg_list=alg_list,
        task_list=ArgChoices.Benchmarks
    )

    # MTOP Solution
    for pb in pb_list:
        result_analysis.mtop_solution(
            pb=pb,
            temp_alg_list=temp_alg_list,
            threshold_p=THRESHOLD,
            style_id=STYLE_ID,
            save=SAVE
        )

    # STOP Solution
    # for pb in pb_list:
    #     result_analysis.stop_solution(
    #         pb=pb,
    #         task_list=ArgChoices.Benchmarks,
    #         temp_alg_list=temp_alg_list,
    #         std_info=STD_INFO,
    #         save=SAVE,
    #         style_id=STYLE_ID
    #     )

    # MTOP Curve
    for pb in tqdm(pb_list, desc='MTOP', ncols=100, leave=False):
        result_analysis.mtop_curve(
            pb=pb,
            use_log_value=LOG_VAL,
            file_suffix=SUFFIX
        )

    # STOP Curve
    # for pb in tqdm(pb_list, desc="STOP", ncols=100, leave=False):
    #     for task_name in tqdm(ArgChoices.Benchmarks, desc='Task', ncols=100, leave=False):
    #         result_analysis.stop_curve(
    #             pb=pb,
    #             task_name=task_name,
    #             use_log_value=LOG_VAL,
    #             file_suffix=SUFFIX
    #         )
