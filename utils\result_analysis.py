# -*- coding:utf-8 -*-
"""
Author:<PERSON><PERSON><PERSON>
References:
    - https://doi.org/10.1177/070674379604100805.
    - https://zhuanlan.zhihu.com/p/30043259
"""

from collections import namedtuple

import pandas
from scipy import stats
from tqdm.auto import trange
from typing import Union

import matplotlib.pyplot as plt
import numpy as np
import os
import pandas as pd
import time

MeanAndSe = namedtuple("MeanAndSe", ["mean", "se"])


class ResultAnalysis:
    def __init__(self, root_path, algor_list,
                 client_num=18, data_size=110, init_size=50, marker_num=10,
                 minimize=True, logvalue=True, obj_alg_alias=None, task_name=None):
        if init_size >= data_size:
            raise ValueError(f"init_size(now={init_size}) must be smaller than data_size(now={data_size})")
        self.logvalue = logvalue
        self.minimize = minimize
        self.root_path = root_path
        self.algor_list = algor_list
        self.client_num = client_num
        self.data_size = data_size
        self.init_size = init_size
        self.marker_num = marker_num
        self.task_name = task_name
        self.obj_alg_alias = obj_alg_alias if obj_alg_alias is not None else algor_list[-1]
        self.file_name = None

        # preprocessing
        self.file_path = self.__list_file_path()

        self.mean_se = None
        self.optimals = None
        self.__get_mean_se_optimals()

        self.grouped_mean_se = None
        self.grouped_optmal = None
        self.__group_by_client_id()


        if self.logvalue:
            self.y_label = "Fitness value (log)"
        else:
            self.y_label = "Fitness value"

    def __list_file_path(self) -> dict:
        print("Getting file list...")
        res = {}
        for alg in self.algor_list:
            alg_path = os.path.join(self.root_path, alg)
            # if not os.path.exists(alg_path):
            #     raise FileExistsError(f"File path {alg_path} does not exist")
            file_name_list = []
            for f_name in os.listdir(alg_path):
                if 'density' in f_name or 'log' in f_name:
                    continue
                file_name_list.append(os.path.join(alg_path, f_name))
            res.update({alg: file_name_list})
        return res

    def __get_mean_se_optimals(self) -> None:
        print("Calculating mean and se...")
        mean_se = {}
        optimals = {}
        for alg_name, path_list in self.file_path.items():
            if not path_list:
                raise Warning(f"The result of algorithm {alg_name} is none")
            whole_data = self.__csv2ndarray(path_list)
            mean_se.update({alg_name: self.__cal_mean_se(whole_data)})
            optimals.update({alg_name: self.__optimal_solutions(whole_data)})

        self.mean_se = mean_se
        self.optimals = optimals

    def __csv2ndarray(self, path_list) -> np.ndarray:
        res = []
        for path in path_list:
            pd_data = pd.read_csv(path)
            col_wise = pd_data[pd_data.columns[1:]].to_numpy()
            row_wise = col_wise.T
            time_data = row_wise[-1]
            row_wise = self.__convergence_data(row_wise[:-1])
            row_wise.append(time_data)
            res.append(row_wise)
        return np.array(res)

    @staticmethod
    def __optimal_solutions(whole_data):
        return whole_data[:,:,-1]

    def __cal_mean_se(self, whole_data) -> MeanAndSe:
        mean = np.mean(whole_data, axis=0)
        se = np.std(whole_data, axis=0)/np.sqrt(20 * 1)
        if self.logvalue:
            mean = np.log10(mean)
            se.fill(0)
        return MeanAndSe(mean, se)

    def __convergence_data(self, mean_mat) -> list:
        conv_mat = []
        if self.minimize:
            for row in mean_mat:
                first = np.min(row[:self.init_size])
                res_list = [first for _ in range(self.init_size)]
                for v in row[self.init_size:]:
                    if v < res_list[-1]:
                        res_list.append(v)
                    else:
                        res_list.append(res_list[-1])
                conv_mat.append(res_list)
        else:
            for row in mean_mat:
                first = np.max(row[:self.init_size])
                res_list = [first for _ in range(self.init_size)]
                for v in row[self.init_size:]:
                    if v > res_list[-1]:
                        res_list.append(v)
                    else:
                        res_list.append(res_list[-1])
                conv_mat.append(res_list)
        return conv_mat

    def __group_by_client_id(self) -> None:
        print("Grouping data by client id...")
        mean_se = self.mean_se
        optimals = self.optimals
        alg_list = self.algor_list

        row_num = len(mean_se[alg_list[0]].mean)
        ms_res = []
        opt_res = []
        for cid in range(row_num):
            curr_client_ms = {}
            curr_client_opt = {}
            for alg in alg_list:
                ms = MeanAndSe(mean_se[alg].mean[cid], mean_se[alg].se[cid])
                curr_client_ms.update({alg: ms})
                curr_client_opt.update({alg: optimals[alg][:,cid]})
            ms_res.append(curr_client_ms)
            opt_res.append(curr_client_opt)
        self.grouped_mean_se = ms_res
        self.grouped_optmal = opt_res

    def compare_convergence_curve(self, save=True, single=True, file_suffix=".png") -> None:
        print("Generating comparison result...")
        if 'single' in self.root_path:
            file_name = f'STOP on {self.task_name}' if self.task_name else 'STOP'
        elif 'multi' in self.root_path:
            file_name = f'MTOP on {self.task_name}' if self.task_name else 'MTOP'
        else:
            file_name = "convergence_curve"
        self.file_name = file_name
        marker = ['x', 'v', '8', 's', '*', 'D', 'P', '^']
        platte = plt.get_cmap("Set1")
        showing_size = self.data_size - self.init_size
        marker_pace = showing_size//self.marker_num
        marker_index = [j for j in range(showing_size) if j != 0 and j % marker_pace == 0]
        x_label = np.arange(showing_size)+1

        loop = trange(self.client_num, ncols=80) if save else range(self.client_num)
        if save:
            if single:
                file_path = self.__gen_saving_path(file_name, single_file=True)
                self.__plot_all_in_one(loop, platte, showing_size, x_label, marker, marker_index, save, file_path, file_suffix)
            else:
                path_list = self.__gen_saving_path(file_name, single_file=False)
                self.__plot_one_by_one(loop, platte, showing_size, x_label, marker, marker_index, save, path_list, file_suffix)

    def __plot_one_by_one(self, loop, platte, showing_size, x_label, marker, marker_index, save, path_list, file_suffix) -> None:
        for cid in loop:
            data = self.grouped_mean_se[cid]
            for i, alg in enumerate(self.algor_list):
                color = platte(i)
                avg = data[alg].mean[-showing_size:]
                se = data[alg].se[-showing_size:]
                r1 = list(map(lambda x: x[0] - x[1], zip(avg, se)))  # 上方差
                r2 = list(map(lambda x: x[0] + x[1], zip(avg, se)))  # 下方差
                if alg == self.algor_list[-1]:
                    alg = self.obj_alg_alias
                plt.plot(x_label, avg, color=color, label=alg, linewidth=1, marker=marker[i], markevery=marker_index)
                plt.fill_between(x_label, r1, r2, color=color, alpha=0.2)

                plt.title(f"Client {cid}")
                plt.xlabel("Communication round")
                plt.ylabel(self.y_label)

            plt.legend()
            if save:
                plt.savefig(f"{path_list[cid]}{file_suffix}")
            else:
                plt.show()
                input("Type Enter to next one")
            plt.close()

    def __plot_all_in_one(self, loop, platte, showing_size, x_label, marker, marker_index, save, file_path, file_suffix):
        if self.client_num == 18:
            row = 6
            col = 3
            fig_size = (18, 24)

        elif self.client_num == 10:
            row = 5
            col = 2
            fig_size = (12, 20)
        else:
            raise NotImplementedError(f"client_num = {self.client_num} not implemented")
        fig, axes = plt.subplots(nrows=row, ncols=col, figsize=fig_size, constrained_layout=True)
        for cid in loop:
            data = self.grouped_mean_se[cid]
            ax = axes[cid // col, cid % col]
            for i, alg in enumerate(self.algor_list):
                color = platte(i)
                avg = data[alg].mean[-showing_size:]
                se = data[alg].se[-showing_size:]
                r1 = list(map(lambda x: x[0] - x[1], zip(avg, se)))  # 上方差
                r2 = list(map(lambda x: x[0] + x[1], zip(avg, se)))  # 下方差
                if alg == self.algor_list[-1]:
                    alg = self.obj_alg_alias
                ax.plot(x_label, avg, color=color, label=alg, linewidth=1, marker=marker[i], markevery=marker_index)
                ax.fill_between(x_label, r1, r2, color=color, alpha=0.2)

                ax.set_title(f"Client {cid}")
                ax.set_xlabel("Communication round")
                ax.set_ylabel(self.y_label)

            ax.legend()
        if self.file_name:
            fig.suptitle(self.file_name)
        if save:
            plt.savefig(f"{file_path}{file_suffix}")
        else:
            plt.show()
            input("Type Enter to next one")
        plt.close(fig)

    def compare_optimal_solution(self, threshold_p=0.05, style_id=0, save=True) -> None:
        obj_alg = self.algor_list[-1]
        res_dict = {"Clients":["Client{:<2}".format(i) for i in range(self.client_num)]}
        for alg in self.algor_list[:-1]:
            res = []
            for cid in range(self.client_num):
                opt_list1 = self.grouped_optmal[cid][alg]
                opt_list2 = self.grouped_optmal[cid][obj_alg]
                mean1 = np.mean(opt_list1)
                mean2 = np.mean(opt_list2)
                diff = mean1-mean2
                _, p = stats.ttest_ind(opt_list1, opt_list2)
                if p > threshold_p:
                    suffix = "≈"
                elif diff > 0:
                    suffix = "+"
                else:
                    suffix = "-"
                res.append("{:.5e}".format(mean1)+suffix)
            res_dict.update({alg: res})

        obj_alg_optimals = [self.optimals[obj_alg][:, cid] for cid in range(self.client_num)]
        obj_alg_mean = np.mean(obj_alg_optimals, axis=1)
        res = ["{:.5e}".format(m) for m in obj_alg_mean]
        res_dict.update({obj_alg: res})

        if save:
            path_name = self.__gen_saving_path("optimal_solution", single_file=True)
            self.__save_to_xlsx(path_name, res_dict, style_id=style_id)
        else:
            self.__console_print(res_dict)

    @staticmethod
    def __console_print(res_dict) -> None:
        pd.set_option('display.colheader_justify', 'center')
        df = pd.DataFrame(res_dict)
        print(df.to_string(index=False))

    def __save_to_xlsx(self, path_name, res_dict, style_id) -> None:
        global_index_mat, solo_index_mat = self.calculate_optimality_index()

        global_counter = np.sum(global_index_mat, axis=0).reshape(1, -1)
        solo_counter = np.sum(solo_index_mat, axis=0).reshape(1, -1)

        df = pd.DataFrame(res_dict, index=None)
        columns = self.algor_list
        columns.insert(0, "Clients")

        global_counter_df = pd.DataFrame(global_counter, columns=columns, index=None)
        solo_counter_df = pd.DataFrame(solo_counter, columns=columns, index=None)
        global_df = pd.concat([df, global_counter_df], ignore_index=True)
        solo_df = pd.concat([df, solo_counter_df], ignore_index=True)
        df.rename(columns={self.algor_list[-1]: self.obj_alg_alias}, inplace=True)

        global_df.iloc[-1, 0] = 'Sum'
        solo_df.iloc[-1, 0] = 'Sum'

        global_styled_df = global_df.style.map(self.highlight_opt, opt_index_mat=global_index_mat, style_id=style_id, src_data=global_df)
        solo_styled_df = solo_df.style.map(self.highlight_opt, opt_index_mat=solo_index_mat, style_id=style_id, src_data=solo_df)

        file_suffix = '.xlsx'
        with pd.ExcelWriter(path_name+file_suffix, engine='openpyxl') as writer:
            global_styled_df.to_excel(writer, index=False, sheet_name='Global')
            solo_styled_df.to_excel(writer, index=False, sheet_name='Solo')

    @staticmethod
    def highlight_opt(val, opt_index_mat, src_data, style_id):
        style_list = [
            "background-color: #c0c0c0",
            "font-weight: bold"
        ]
        if style_id > len(style_list) - 1:
            raise ValueError("support 2 styles:\n0 --> grey background\n 1 --> bold")

        all_loc = np.where(val == src_data)
        loc = all_loc[0][:1], all_loc[1][:1]
        is_opt = np.all(opt_index_mat[loc])
        return style_list[style_id] if is_opt else ''

    def calculate_optimality_index(self):
        mean_dict = {}
        for alg in self.algor_list:
            res = []
            for cid in range(self.client_num):
                opt_list = self.grouped_optmal[cid][alg]
                res.append(np.mean(opt_list))
            mean_dict.update({alg: res})

        df = pd.DataFrame.from_dict(mean_dict)
        data_mat = df.to_numpy()

        mat_shape = data_mat.shape[0], data_mat.shape[1]
        solo_index_mat = np.zeros(shape=mat_shape, dtype=bool)
        global_index_mat = np.zeros(shape=mat_shape, dtype=bool)

        row, col = solo_index_mat.shape
        min_idx = np.argmin(data_mat, axis=1)
        for i in range(row):
            global_index_mat[i, min_idx[i]] = True
            obj_val = data_mat[i, -1]
            for j in range(col-1):
                if data_mat[i, j] < obj_val:
                    solo_index_mat[i, j] = True
        solo_index_mat = global_index_mat+solo_index_mat

        # row+1: the last row is the count of best solution of each algorithm
        # col+1: the first col is the index of Client
        add_row = np.zeros(shape=solo_index_mat.shape[1], dtype=bool)
        solo_index_mat = np.vstack((solo_index_mat, add_row))
        global_index_mat = np.vstack((global_index_mat, add_row))

        add_col = np.zeros(shape=solo_index_mat.shape[0], dtype=bool).reshape(-1, 1)
        solo_index_mat = np.hstack((add_col, solo_index_mat))
        global_index_mat = np.hstack((add_col, global_index_mat))
        return global_index_mat, solo_index_mat

    def __gen_saving_path(self, file_name, single_file=True) -> Union[list, str]:
        save_root = os.path.join(self.root_path, f"AAA_{self.file_name}_{time.strftime('%Y-%m-%d')}")
        if not os.path.exists(save_root):
            os.makedirs(save_root)
        path = os.path.join(save_root, f"{file_name}_{time.strftime('%H_%M_%S')}")
        if single_file:
            return path
        else:
            os.makedirs(path)
            file_names = [f"Client-{i}" for i in range(self.client_num)]
            path_list = [os.path.join(path, fname) for fname in file_names]
            return path_list

    def compare_time_consumption(self, save=True, file_suffix='.png') -> None:
        res = []
        for alg in self.algor_list:
            time_consumption = self.grouped_mean_se[-1][alg].mean[-1]
            res.append(np.sum(time_consumption))
        plt.bar(self.algor_list, res)
        plt.title("Time consumption comparison")
        plt.xlabel("Algorithms")
        plt.ylabel("Time consumption(s)")
        if save:
            file_name = self.__gen_saving_path("time_consumption", single_file=True)
            plt.savefig(f"{file_name}{file_suffix}")
        else:
            plt.show()
        plt.cla()
