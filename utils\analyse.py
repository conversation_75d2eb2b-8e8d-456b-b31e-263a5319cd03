import os
import csv
import math # 用于 float('inf')

def find_optimal_fitness_per_file(filepath, num_clients=18, max_rows=None):
    """
    在一个CSV文件中为每个客户端找到最优（最小）适应度值。

    Args:
        filepath (str): CSV文件的路径。
        num_clients (int): 客户端的数量。
        max_rows (int, optional): 最大处理行数。如果为None，则自动检测实际评估次数。

    Returns:
        list: 包含该文件中每个客户端最优适应度值的列表，如果文件无法处理则返回None。
    """
    client_optimal_fitness_in_file = [float('inf')] * num_clients
    try:
        with open(filepath, 'r', newline='') as csvfile:
            reader = csv.reader(csvfile)
            header = next(reader) # 跳过表头

            # 确定客户端列的索引，假设它们从第二列开始
            # 例如, client0 在索引 1, client1 在索引 2, ..., client17 在索引 18
            # CSV列：轮数, client0, client1, ..., client17, time

            # 如果没有指定max_rows，先读取所有数据来确定实际行数
            all_rows = list(reader)
            actual_data_rows = len([row for row in all_rows if row and len(row) > num_clients])

            if max_rows is None:
                # 动态确定处理行数：使用实际有效数据行数
                max_rows_to_process = actual_data_rows
                print(f"文件 '{os.path.basename(filepath)}' 检测到 {actual_data_rows} 行有效数据，将处理全部数据")
            else:
                # 使用指定的行数，但不超过实际数据行数
                max_rows_to_process = min(max_rows, actual_data_rows)
                print(f"文件 '{os.path.basename(filepath)}' 将处理前 {max_rows_to_process} 行数据（共 {actual_data_rows} 行）")

            rows_processed_for_file = 0
            for row_index, row_data in enumerate(all_rows):
                if not row_data or len(row_data) <= num_clients: # 跳过空行或数据不完整的行
                    continue

                # 只处理指定数量的行
                if row_index < max_rows_to_process:
                    try:
                        # 第一个元素是轮数索引，之后是客户端数据
                        for client_idx in range(num_clients):
                            # row_data[0] 是轮数, row_data[1] 是 client0, ...
                            fitness_value_str = row_data[client_idx + 1]
                            fitness_value = float(fitness_value_str)
                            if fitness_value < client_optimal_fitness_in_file[client_idx]:
                                client_optimal_fitness_in_file[client_idx] = fitness_value
                        rows_processed_for_file +=1
                    except (ValueError, IndexError) as e:
                        # 打印警告，但继续处理文件的其余部分和其他客户端
                        print(f"警告：处理文件 '{filepath}' 第 {row_index + 2} 行, 客户端 {client_idx} 时出错: {e}。数据: '{row_data[client_idx + 1] if client_idx + 1 < len(row_data) else 'N/A'}'")
                        continue

            if rows_processed_for_file == 0:
                print(f"警告：文件 '{filepath}' 中没有成功处理任何数据行。")
                return None

            print(f"文件 '{os.path.basename(filepath)}' 成功处理了 {rows_processed_for_file} 行数据")

            # 如果某个客户端在所有轮次中都没有有效数据，其最优值仍为inf
            # 我们可以选择将其替换为NaN或特定标记，或在后续平均计算中处理
            for i in range(num_clients):
                if client_optimal_fitness_in_file[i] == float('inf'):
                    print(f"警告：文件 '{filepath}' 中的客户端 {i} 没有找到有效的适应度值。")
                    # client_optimal_fitness_in_file[i] = float('nan') # 或者保持inf，平均时处理

            return client_optimal_fitness_in_file
        
    except FileNotFoundError:
        print(f"错误：文件 '{filepath}' 未找到。")
        return None
    except StopIteration: # 文件为空或只有表头
        print(f"错误：文件 '{filepath}' 为空或只有表头。")
        return None
    except Exception as e:
        print(f"处理文件 '{filepath}' 时发生未知错误：{e}")
        return None


def process_all_files_and_average(folder_path, output_filename="average_optimal_fitness_corrected.csv", max_rows=None):
    """
    处理文件夹中的所有CSV文件，计算每个客户端的平均最优适应度值。

    Args:
        folder_path (str): 包含CSV文件的文件夹路径
        output_filename (str): 输出文件名
        max_rows (int, optional): 最大处理行数。如果为None，则动态检测每个文件的实际评估次数
    """
    all_csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv') and f.startswith('run')]
    if not all_csv_files:
        print(f"在文件夹 '{folder_path}' 中没有找到CSV文件。")
        return

    num_clients = 18
    # 用于累加每个客户端从各个文件中找到的最优适应度值
    total_optimal_fitness_sums = [0.0] * num_clients
    # 记录每个客户端有多少个文件为其贡献了有效的最优值
    files_contributing_per_client = [0] * num_clients

    print(f"找到 {len(all_csv_files)} 个CSV文件进行处理...")
    if max_rows is None:
        print("使用动态行数检测模式：将根据每个文件的实际评估次数进行处理")
    else:
        print(f"使用固定行数模式：每个文件最多处理 {max_rows} 行数据")

    for filename in all_csv_files:
        filepath = os.path.join(folder_path, filename)
        print(f"正在处理文件: {filename}...")
        optimal_fitness_this_file = find_optimal_fitness_per_file(filepath, num_clients, max_rows)

        if optimal_fitness_this_file:
            for i in range(num_clients):
                if optimal_fitness_this_file[i] != float('inf') and not math.isnan(optimal_fitness_this_file[i]):
                    total_optimal_fitness_sums[i] += optimal_fitness_this_file[i]
                    files_contributing_per_client[i] += 1
                # else:
                    # print(f"文件 {filename} 的客户端 {i} 没有有效的最优值，不计入平均。")


    average_client_fitness = [0.0] * num_clients
    for i in range(num_clients):
        if files_contributing_per_client[i] > 0:
            average_client_fitness[i] = total_optimal_fitness_sums[i] / files_contributing_per_client[i]
        else:
            average_client_fitness[i] = float('nan') # 或者 0.0，或标记为无数据
            print(f"警告：客户端 {i} 在所有文件中都没有找到有效的适应度值。")

    output_filepath = os.path.join(folder_path, output_filename)
    try:
        with open(output_filepath, 'w', newline='') as outfile:
            writer = csv.writer(outfile)
            writer.writerow(['ClientIndex', 'AverageOptimalFitness'])
            for i in range(num_clients):
                writer.writerow([f'client{i}', average_client_fitness[i] if not math.isnan(average_client_fitness[i]) else 'N/A'])
        print(f"处理完成。结果已保存到 '{output_filepath}'")
    except IOError as e:
        print(f"写入输出文件 '{output_filepath}' 时出错：{e}")


def process_multiple_directories(directory_paths, output_filename="average_optimal_fitness_corrected.csv", max_rows=None):
    """
    处理多个目录，对每个目录分别进行分析。

    Args:
        directory_paths (list): 要处理的目录路径列表
        output_filename (str): 输出文件名
        max_rows (int, optional): 最大处理行数。如果为None，则动态检测每个文件的实际评估次数
    """
    if not directory_paths:
        print("错误：没有提供要处理的目录路径。")
        return

    print(f"开始处理 {len(directory_paths)} 个目录...")
    if max_rows is None:
        print("使用动态行数检测模式：将根据每个文件的实际评估次数进行处理")
    else:
        print(f"使用固定行数模式：每个文件最多处理 {max_rows} 行数据")
    print("=" * 60)

    successful_count = 0
    failed_count = 0

    for i, folder_path in enumerate(directory_paths, 1):
        print(f"\n[{i}/{len(directory_paths)}] 正在处理目录: {folder_path}")
        print("-" * 50)

        if not os.path.isdir(folder_path):
            print(f"错误：目录 '{folder_path}' 不存在，跳过处理。")
            failed_count += 1
            continue

        try:
            process_all_files_and_average(folder_path, output_filename, max_rows)
            successful_count += 1
            print(f"目录 '{folder_path}' 处理完成。")
        except Exception as e:
            print(f"处理目录 '{folder_path}' 时发生错误：{e}")
            failed_count += 1

    print("\n" + "=" * 60)
    print(f"处理完成！成功处理 {successful_count} 个目录，失败 {failed_count} 个目录。")


def auto_detect_result_directories():
    """
    自动检测当前目录下的结果目录
    按照格式：results_dles_ktp{kt_agg_prob}_init{population_size}_fe{max_evaluations}
    """
    current_dir = os.getcwd()
    result_dirs = []

    print("正在扫描当前目录下的结果目录...")

    for item in os.listdir(current_dir):
        if os.path.isdir(item) and (item.startswith('results_dles_ktp') or item.startswith('results_dles_sim')):
            print(f"发现结果目录: {item}")

            # 检查是否包含子目录（如iid, niid2等）
            try:
                subdirs = [d for d in os.listdir(item) if os.path.isdir(os.path.join(item, d))]
                if subdirs:
                    for subdir in subdirs:
                        full_path = os.path.join(item, subdir)
                        # 检查是否包含CSV文件
                        try:
                            csv_files = [f for f in os.listdir(full_path) if f.endswith('.csv') and f.startswith('run')]
                            if csv_files:
                                result_dirs.append(full_path)
                                print(f"  - 子目录 {subdir} 包含 {len(csv_files)} 个CSV文件")
                        except PermissionError:
                            print(f"  - 无法访问子目录 {subdir}")
                            continue
                else:
                    # 直接检查主目录是否包含CSV文件
                    csv_files = [f for f in os.listdir(item) if f.endswith('.csv') and f.startswith('run')]
                    if csv_files:
                        result_dirs.append(item)
                        print(f"  - 主目录包含 {len(csv_files)} 个CSV文件")
            except PermissionError:
                print(f"  - 无法访问目录 {item}")
                continue

    return result_dirs


if __name__ == "__main__":
    import sys
    import argparse

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='动态分析FMDLES实验结果')
    parser.add_argument('--max_rows', type=int, default=None,
                       help='最大处理行数。如果不指定，将根据每个文件的实际评估次数动态处理')
    parser.add_argument('--dirs', nargs='*', default=None,
                       help='要处理的目录路径列表。如果不指定，将自动检测当前目录下的结果目录')
    parser.add_argument('--output', default="average_optimal_fitness_corrected.csv",
                       help='输出文件名')

    args = parser.parse_args()

    # 确定要处理的目录
    if args.dirs:
        target_folders = args.dirs
        print(f"从命令行参数获取到 {len(target_folders)} 个目录路径。")
    else:
        # 自动检测结果目录
        target_folders = auto_detect_result_directories()
        if target_folders:
            print(f"自动检测到 {len(target_folders)} 个结果目录：")
            for folder in target_folders:
                print(f"  - {folder}")
        else:
            print("未检测到任何结果目录，使用默认目录列表...")
            # 默认目录列表（保持向后兼容）
            target_folders = [
                'results_dles_ktp0.5_init10_fe110',
                'results_dles_ktp0.5_init10_fe500',
                # 新格式示例
                'results_dles_ktp0.5_init10_fe110_mt',
                'results_dles_ktp0.5_init20_fe500_mt',
            ]

    if not target_folders:
        print("错误：没有找到要处理的目录。")
        sys.exit(1)

    # 处理多个目录
    process_multiple_directories(target_folders, args.output, args.max_rows)
