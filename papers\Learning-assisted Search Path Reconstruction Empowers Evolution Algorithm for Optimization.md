# Learning-assisted Search Path Reconstruction Empowers Evolution Algorithm for Optimization

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fellow, IEEE, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, Senior Member, IEEE

Abstract—Evolutionary algorithms serve as a pivotal tool in addressing black-box problems, finding widespread applications across diverse academic disciplines and engineering domains. Despite their utility, these algorithms often confront challenges when navigating complex search spaces, impeding a comprehensive exploration of potential solutions. Solely depending on the algorithm’s exploration abilities falls short of fully harnessing the rich information contained within search spaces. To unlock the full potential of solution spaces, we introduce a deep learning method for the reconstruction of the search path. Specifically, discrete data sampled by evolutionary operators during the exploration process are collected, and a uniquely designed fully connected neural network is employed to reconstruct the exploration paths. The neural network’s robust fitting capability facilitates the transformation of initially discrete sampled information into a continuous form. By capitalizing on the reconstructed solution space information, the algorithm excels in identifying superior solutions. We refer to this method of deep learning-based search path reconstruction evolution strategy algorithm (DLES). The effectiveness of DLES is validated across multiple datasets, including CEC 2014, CEC 2018, CEC 2022 and BBOB. Experimental results, compared to several state-ofthe-art algorithms, affirm the superiority of the DLES algorithm.

Index Terms—Evolutionary computation, deep learning, search space reconstruction, artificial neural network, single-objective optimization.

The primary goal of EC is to optimize a given objective function and identify the optimal solution within defined constraints [3]. Nevertheless, when dealing with extensive representational data like images and text, EC approaches often face limitations and overpower. Therefore, deep learning (DL) was introduced to tackle these daunting tasks in the early 21st century. DL has witnessed remarkable success, particularly in computer vision, natural language processing, image and speech recognition, etc [4], [5], [6]. Deep neural networks are trained to automatically update connection weights to achieve better learning results and handle tasks more effectively [7]. The rapid advancement of DL is driven by factors such as large-scale datasets, enhanced hardware capabilities, and improved algorithms, positioning it as a key driving force in the field of artificial intelligence [8], [9].

# I. Introduction

Although EC and DL have different tasks and goals, the interplay between the two branches has been a subject of keen scholarly interest. A superior DL model can automatically fine-tune neural network weights and parameters to achieve desired outcomes. Meanwhile, EC is geared toward exploring solution spaces to discover optimal solutions without an inherent learning process [10]. Borrowing EC to evolve the topology of neural networks or using neural networks to empower learning capabilities within EC are exciting sources of inspiration. Therefore, researchers have aimed to unify EC and DL to harmonize and complement each other to generate a more powerful artificial intelligence tool [11].

E aVdOdrLeUssTIcOomNpAleRxYocpotimpiuztattiion p(rEoCb)leismas [p1o]w. eIrnfsupl troedo btoy biological evolution, evolutionary algorithms assess the fitness of real-valued solutions to promote the progress of search. The best solution vectors are retained while other solutions that are not favorable to evolution are discarded. The surviving high-quality solutions are subsequently subjected to crossover and mutation to generate the next generation. This crossover approach addresses the limitations of traditional algorithms, which struggle to solve problems involving a large number of variables and are prone to prolonged convergence, making them difficult to train [2].

This research was partially supported by the Japan Society for the Promotion of Science (JSPS) KAKENHI under Grant JP23K24899, and Japan Science and Technology Agency (JST) Support for Pioneering Research Initiated by the Next Generation (SPRING) under Grant JPMJSP2145. (Corresponding authors: Zhenyu Lei and Shangce Gao)

Y. Song, K. Wang, Z. Lei and S. Gao are with the Faculty of Engineering, University of Toyama, Toyama-shi, 930-8555, Japan. (E-mail: <EMAIL>; greskofairy $@$ gmail.com; <EMAIL>; gaosc $@$ eng.u-toyama.ac.jp). Z. H. Zhan is with the College of Artificial Intelligence, Nankai University, Tianjin 300350, China. (E-mail: <EMAIL>).

The integration of evolutionary algorithms with neural networks collectively represents a burgeoning field, poised to mitigate inherent limitations in both domains. This synergy heralds a promising trajectory for the refinement of evolutionary algorithm exploration and exploitation, as well as the enhancement of neural networks. The predominant emphasis in current research on the integration of evolutionary algorithms and deep learning is the optimization of algorithmic hyperparameters [12], [13]. Additionally, some studies concentrate on leveraging surrogate models to emulate abstract search spaces [14], facilitating effective exploration within broader search domains. This emphasis contributes to the improved sampling efficiency of algorithms within the expansive search space. There is a noticeable lack of attention directed towards modeling the authentic search space to extract greater solutions. Hence, we investigate the feasibility and impact of the network in reconstructing the authentic solution space on the population.

Fig. 1 provides a visual representation of utilizing a neural network to reconstruct the search space based on the sampling results of evolutionary operators within the authentic search space, aiming to obtain higher-quality solutions. The points in the figure represent the sampling results of the algorithm in the search space. The results of the network reconstruction are shown on the right side of the figure, with colors ranging from red to blue. The bluer color denotes the higher quality of solutions. While the algorithm samples discretely within the search space, the results by network reconstruction become continuous. Observably, the network demonstrates the capability to discern solutions of superior quality within the search space through the reconstruction of algorithmically sampled results. In this paper, we propose a deep learningbased search path reconstruction evolution strategy algorithm (DLES) that harnesses the formidable fitting capabilities of neural networks to reconstruct the authentic search space, thereby enhancing the quality of the solution. Distinguishing itself from other methods that incorporate neural networks in EC, our approach acquires superior individuals directly from the network based on its reconstruction results to the search path. To the best of our knowledge, we are the first to employ deep learning for reconstructing the authentic search path. The main contributions of this work include the following:

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/f00fdd24f66ffa2da907afcd3c3810fbe72a6f90bc4f43a75ca52676965358ed.jpg)  
Fig. 1. Visualization of algorithm sampling and network reconstruction.

1) Innovatively, we propose a novel DLES algorithm, it employs a novel full-connected neural network to reconstruct the search path. The network utilizes the fitness as its input to construct individual information as the output. It is noteworthy that the input dimension of the network is set to one, deviating significantly from conventional network architectures.   
2) Initially, we design the neural network to address a discrete sample of solution space. It vastly extends the sample, making the algorithm gain a more nuanced understanding of the search path, thereby assisting in the discovery of superior solutions.   
3) We perform massive experiments to verify the performance of DLES on diverse CEC and BBOB test sets, compared with state-of-the-art algorithms, which demonstrate DLES’s superiority. Besides, we comprehensively analyze the impact of hyperparameters and space reconstruction networks to achieve stable and robust performance.

Section II gives a preliminary about the evolution strategy and the related work of learning evolutionary computation. Section III details the DLES. Section IV illustrates the experiments with results and analyses. Finally, the conclusion is

provided in Section V.

# II. Preliminaries

This section introduces the evolution strategy and the related work of learning-based evolutionary computation.

# A. Evolutionary Computation and Evolution Strategies

EC encompasses optimization methods inspired by natural selection, effectively solving complex, nonlinear problems through exploration and exploitation of vast search spaces [15]. Among these, Evolution Strategies (ES), such as Covariance Matrix Adaptation (CMA-ES), excel in continuous optimization, especially in high-dimensional and multimodal problems [16], [17]. ES achieves robustness by focusing on mutation without relying on crossover, making it adaptable for diverse, challenging landscapes. A more detailed introduction to EC and ES can be found in Supplementary File Section I.

# B. Learning-based Evolutionary Computation

In recent years, the challenges of incorporating learning capabilities into evolutionary algorithms have been steadily addressed through the emergence of various learning models and advancements in neural networks. Currently, there are two primary directions for modifying evolutionary algorithms through learning. The first approach involves employing learning models, such as transfer learning and reinforcement learning, to enhance the performance of evolutionary algorithms. Transfer learning enables the reuse of knowledge from related tasks to accelerate optimization in new problem domains, while reinforcement learning leverages agent-based decision-making to improve adaptability and dynamic control in optimization scenarios. The second approach focuses on integrating neural networks into the evolutionary search process, where neural networks act as adaptive tools for modeling complex problem landscapes, generating high-quality candidate solutions, and guiding the search process through learned patterns and behaviors. These two approaches reflect ongoing efforts to combine the strengths of evolutionary algorithms and deep learning, marking a promising advancement in the field.

In the methods employing learning models evolutionary transfer optimization has emerged as a promising approach within the EC community [18]. This cross-domain transfer learning method has proven effective in complex optimization tasks and has led to the development of branches such as sequential optimization and multi-task optimization. Jiang et al. [19] propose a novel algorithmic framework that integrates streaming transfer learning with memory mechanisms. By leveraging historical experience, this framework enables the population to explore the optimal potential space, significantly enhancing optimization speed without sacrificing solution quality. Qiao et al. [20] develop an evolutionary multi-task model to address constrained multi-objective problems. The successful application of transfer learning in optimization has also spurred interest in integrating reinforcement learning into the EC framework, resulting in numerous reinforcement learning-assisted evolutionary algorithms. A distinctive feature of reinforcement learning is its agent-based learning process grounded in Markovian decision-making to maximize rewards [21]. Sun et al. [22] innovatively incorporate a deep neural network as a controller for control parameters, coupling it with a reinforcement policy gradient algorithm to optimize the best parameters. Zhang et al. [23] propose a hybrid adaptive parameter control framework that combines Q-learning and deep Qlearning to regulate agents. Chrabaszcz et al. [24] demonstrate that ES can serve as a viable alternative to reinforcement learning algorithms in tackling a range of challenging deep reinforcement learning problems.

In addition to the previously discussed learning models, neural networks have recently been directly integrated in EC. Ismayilov and Topcuoglu [25] propose a dynamic multiobjective optimization algorithm based on artificial neural networks, demonstrating effective performance on dynamic workflow scheduling problems. This algorithm uses a multilayer neural network to train paired solution outputs, thereby capturing high-quality solutions. Zhan et al. [26] introduce a network-assisted learning-evolutionary algorithm that provides the evolutionary operator with the ability to learn and generate promising next-generation individuals. This learning operator draws from numerous successful solutions and training to enhance the overall evolutionary search process. Luo and Oyedele [27] combine a hybrid genetic algorithm and long short-term memory neural network to forecast building energy consumption accurately and robustly, utilizing weather and time data, with the genetic algorithm optimizing the network architecture and hyperparameters. Enes et al. [28] propose the GAEnsemble model, which leverages a genetic algorithm to integrate the weights of multiple convolutional neural network models for addressing the crop pest classification problem.

Employing learning models provides advantages such as faster training, adaptability, and optimal decision-making. However, it requires extensive learning to acquire effective knowledge, leading to challenges such as high computational cost and slow convergence [21]. Similarly, integrating neural networks into the evolutionary search process enhances search efficiency and enables the learning of complex patterns. Nevertheless, the inherent volatility of neural networks and their high demand for computational resources result in limitations, including training instability and increased computational complexity [29]. While both approaches have their respective advantages and limitations, significant potential remains to explore these directions further. Maximizing the advantages of neural networks in evolutionary computation continues to be a promising and compelling area of research.

# III. The proposed method

The section describes the motivation and the details of DLES. The proposed DLES reconstructs the authentic search path with a deep learning network, enhancing the algorithm’s capability to navigate complex search spaces. This assists the algorithm in locating more optimal solutions.

# A. Motivation

Evolutionary algorithms and neural networks are powerful tools for solving black-box problems. Combining them leverages their strengths to better handle complex problems and gain deeper insights into intricate search spaces [30].

Complex search spaces present a significant challenge, making direct simulation impractical. Surrogate-assisted evolutionary methods for dimensionality reduction struggle to capture all features of such spaces and require substantial computational resources, leading to high costs [14], [31], [32]. A more practical approach involves partitioning the solution space initially and selectively focusing on key regions within it [33], [34].

During the search for the optimal individual, evolutionary algorithms follow two main stages: exploration and exploitation [35]. The success of the exploration phase is crucial for the effectiveness of the subsequent exploitation. While evolutionary algorithms sample the search space to infer its structure, the discrete nature of the sampling process makes it difficult to obtain a complete and accurate representation. This limitation may cause the algorithm to miss valuable areas and become trapped in local optima.

To address this challenge, we propose the DLES, leveraging the powerful non-linear fitting capability of neural networks to reconstruct these exploration paths. Exploration paths are the sequences of sampled points generated by the algorithm as it explores the solution space. By reconstructing these paths, DLES captures and utilizes the valuable information embedded within the exploration process. While the algorithm explores the solution space extensively, the exploration path occupies a significantly smaller portion compared to the full space. By focusing on reconstructing the exploration path, we substantially reduce the workload for network reconstruction, enabling us to complete the task with less complex networks. The goal is to discover high-quality areas overlooked by the algorithm due to insufficient exploration. By reconstructing the paths, we can achieve a more effective fit for authentic search spaces, providing the algorithm with a better potential area and reducing the risk of falling into local optima [36].

# B. Deep Learning-based Search Space Reconstruction Evolution Strategy

Building upon the concept of deep learning-based search path reconstruction empowers evolution algorithms for optimization, we introduce DLES. The overall structure and flow framework of DLES is depicted in Fig. 2. These components are executed in a sequential manner, comprising three distinct phases. The first phase, the evolutionary operator for exploration, explores the solution space and performs data collection and processing. In the subsequent phase, network reconstruction, the collected data is employed to reconstruct the solution space, thus facilitating the identification of more promising solutions for further exploration. The final phase, hybrid sampling for exploitation, leverages the network-generated solutions to execute the final optimization process. To be specific,

1) Exploration Sampling: The primary focus of this stage involves conducting a thorough exploration of the search space using an evolutionary operator sampling and systematically collecting a dataset. The goal is to facilitate comprehensive

Exploration Sampling Begin   
Initialising individuals Fitness Better individual Fitness 6 Exploration   
Network prediction Final solution Fitness Sampling Fitness Dataset No Data collection Evolutionary Operator Times < T?   
Network Reconstruction Yes Network Reconstruction   
Fitness No Finish training? Input Output Yes Layer Hidden Layers Layer Get promising Hybrid Exploitation individuals Hybrid   
： CMA-ES Exploitation Univariate End

exploration by the algorithm, capturing a diverse set of highly representative data points to construct a dataset suitable for network learning. This approach ensures the effective reconstruction of the search path.

2) Network Reconstruction: After collecting a feasible dataset, a neural network reconstructs the authentic search space, revealing solutions with higher potential. The network extensively leverages the exploration of sampled datasets for profound learning and fitting. Upon concluding the reconstruction process, the network can generate solutions with elevated exploitation possibilities rooted in the collected dataset.

3) Hybrid Exploitation: As the final stage of the DLES, a hybrid sampling strategy is employed to exploit the solutions generated in the network predictions. This method amalgamates the benefits of both single-sampling and multi-sampling, augmenting search efficiency and result accuracy.

# C. Exploration Sampling

In the exploration section, DLES utilizes traditional evolutionary operators, exploring the search space through sampling and gathering pertinent information to construct a dataset suitable for neural network learning.

First, define the evaluation function $F ( \cdot )$ based on a optimization problem, initialize the population $P ^ { 0 }$ , and set the current number of fitness evaluations $\begin{array} { r } { N F E s = 0 } \end{array}$ ). During the exploration process, the population undergoes a cumulative total of $T$ iterations. The population $P ^ { t }$ at generation $t$ comprises $N$ individuals, each denoted as $y _ { i } ^ { t }$ , and each individual has a dimension of $D$ . DLES uses the evolutionary operator $\rho ( \cdot )$ to generate a new population containing $N$ individuals, and the new population is then combined with the existing population to form the candidate set $P ^ { t ^ { \prime } }$ . The number of individuals in $P ^ { t ^ { \prime } }$ is $M$ , where $M \ : = \ : 2 N$ . Evaluating all candidates in $P ^ { t ^ { \prime } }$ costs the evaluation function $F ( P ^ { t ^ { \prime } } )$ to obtain the fitness values $\mathcal { F }$ . Based on the fitness values, $N$ individuals are selected from $P ^ { t ^ { \prime } }$ to constitute the new population $P ^ { t + 1 }$ . By continually updating the population through the utilization of evolutionary operators, DLES samples within the solution space and then comprehensively explores it. Simultaneously, it gathers relevant information, forming a Dataset conducive to neural network learning.

Due to the intrinsic characteristics of evolutionary operators in the sampling process, the algorithm iteratively endeavors to optimize specific locations for the discovery of improved solutions. However, this iterative procedure may lead to generating a substantial amount of redundant information. To enhance the efficiency and effectiveness of network reconstruction, DLES records the information of the current best individual $( y _ { b e s t } )$ and its corresponding fitness value $( f _ { b e s t } )$ upon detecting a decrease in fitness. This means that the algorithm updates its records only when a superior solution is identified compared to the previous best. By doing so, DLES ensures that it consistently retains the most optimal solution encountered throughout the search process. This information forms a dataset available for network learning. This decision avoids introducing excessive redundant information, ensuring that the neural network not only learns valuable information but fits the exploration path well. Throughout this process, the operators persistently attempt, make errors, and learn, progressively accumulating an understanding of the solution space. Such iterative procedures contribute to the stepwise optimization of the population, providing valuable learning Dataset for the neural network.

In DLES, the evolutionary operator $\rho ( \cdot )$ is specifically implemented using the optimized univariate marginal distribution algorithm continuous (UMDAc) [37]. UMDAc exhibits high computational efficiency and operates with minimal interactions between variables. By sampling from the learned marginal distributions, UMDAc effectively balances exploration and exploitation, facilitating a thorough exploration of the search space. This balance is crucial for ensuring comprehensive coverage within the solution space. Additionally, UMDAc demonstrates robustness to noise and varying problem conditions, enhancing its effectiveness across diverse optimization scenarios [38].

This particular optimized $\rho ( \cdot )$ increases the weight of the best individual $y _ { b e s t }$ when computing the mean $\mu . \mu _ { d }$ is to set the weighted sum of the best $N$ solutions on each variable $x _ { d }$ ,

$$
\mu _ { d } = \sum _ { n = 1 } ^ { N } \omega _ { n } x _ { d n } .
$$

The weight $\omega _ { n }$ are determined by following equations:

$$
\omega _ { n } ^ { \prime } = \ln ( N { + } 0 . 5 ) - \ln ( n ) , \quad n \in [ 1 , N ]
$$

$$
\omega _ { n } = \frac { \omega _ { n } ^ { \prime } } { \sum _ { i = 1 } ^ { N } \omega _ { i } ^ { \prime } } .
$$

This modification accelerates convergence by placing more emphasis on the best-performing individuals, thereby improving the algorithm’s convergence efficiency and stability. The exploration pseudocode is presented in Algorithm 1.

# D. Network Reconstruction

In the network reconstruction, a neural network utilizes data collected during the exploration process to reconstruct the authentic search path. The algorithm continuously samples and explores the search space, with the trajectory formed by the sampled points referred to as the search path. Reconstructing the search path involves feeding this path information into a neural network, leveraging its powerful fitting capabilities to learn the details along the path. Through the powerful fitting capabilities of the neural network, the discrete information obtained from sampling is transformed into continuous information. This process is referred to as the reconstructed search path, helps the algorithm better understand the search space. Specifically, by learning from the sampled outcomes of evolutionary algorithms, neural networks can more directly and efficiently comprehend the structure of the search space. This capability enables neural networks to discover individuals situated along the exploration path, those that conventional evolutionary algorithms might overlook. These individuals exhibit greater exploitation potential.

In this research, we employ neural networks to reconstruct the paths in the search space by reverse deducing fitness value $f$ , thereby providing an individual $( y )$ with enhanced developmental potential. Specifically, the neural network takes $f$ as input and predicts individual information $( \hat { y } )$ in a $D /$ - dimensional space. The network comprises three fully connected layers, it is formulated as follows:

$$
\begin{array} { r l } & { n e t ^ { ( i n ) } = W ^ { ( i n ) } f + b ^ { ( i n ) } } \\ & { n e t ^ { ( h ) } = t a n h ( W ^ { ( h ) } n e t ^ { ( i n ) } + b ^ { ( h ) } ) } \\ & { n e t ^ { ( o u t ) } = W ^ { ( o u t ) } n e t ^ { ( h ) } + b ^ { ( o u t ) } , } \end{array}
$$

where $W$ and $b$ represent the weights and bias of networks, respectively. $n e t ^ { ( i n ) } , \ n e t ^ { ( h ) }$ , and $\ n e t ^ { ( o u t ) }$ represents the output of the input, hidden, and output layer. Notably, the network may include a different number of hidden layers. In hidden layers, the hyperbolic tangent function (tanh) is employed as the activation function,

$$
t a n h ( x ) = \frac { e ^ { x } - e ^ { - x } } { e ^ { x } + e ^ { - x } } .
$$

The tanh activation function is chosen based on numerical testing (Supplementary File Section III.A show the details). This function excels at handling negative data, producing output within the range of $[ - 1 , 1 ]$ . This characteristic makes it approach linearity near the zero point, contributing to mitigating the issue of gradient vanishing. The input layer has a size of 1, the output layer has a size of $D$ . Through hidden layers, the neural network maps the input fitness value to a high-dimensional space and then projects the high-dimensional results back to the corresponding $D$ -dimensional space.

During the training phase, the neural network optimizes the weight matrices and bias terms to minimize the difference between $( \hat { y } )$ and $y$ by the loss function. After training completion,

Input: Initialization: $P ^ { 0 } \sim \mathcal { U }$ , $P ^ { 0 } \in R ^ { D \times N }$ , $D a t a s e t = \emptyset$ , $N F E s = 0$ , an evaluation function $F ( \cdot )$ and an   
evolutionary operator $\rho ( \cdot )$   
Output: Dataset, NFEs and $f _ { b e s t }$   
1: $\mathcal { F }  F ( P ^ { 0 } )$ ;   
2: select $f _ { b e s t }$ from $\mathcal { F }$ ;   
3: $N F E s + = N$ ;   
4: select $y _ { b e s t }$ from $P ^ { 0 }$ by $f _ { b e s t }$ ; //optimal individual 5: Add $( y _ { b e s t } , f _ { b e s t } )$ in Dataset;   
6: for $t$ from 0 to $T$ do   
7: $P ^ { t ^ { \prime } } = \rho ( P ^ { t } )$ , $P ^ { t ^ { \prime } } \in R ^ { D \times M } , M = 2 N ;$   
8: get $y _ { b e s t } ^ { t } \mathrm { ~ , ~ } f _ { b e s t } ^ { t }$ and $P ^ { t + 1 }$ from sorted $P ^ { t ^ { \prime } }$   
$P ^ { t + 1 } = P _ { i } ^ { t ^ { \prime } } \mid i \in [ 1 , N ] ;$   
9: $N F E s + = ( M - N )$ ;   
10: if $f _ { b e s t } ^ { t } < f _ { b e s t }$ then   
11: Add $( \boldsymbol { y } _ { b e s t } ^ { t } , f _ { b e s t } ^ { t } )$ in Dataset;   
12: fbest = fbtest;   
13: end if   
14: end for

the neural network, by fitting the information of the search space, can predict individual information corresponding to the input fitness value. When provided $y$ with the smaller fitness minimum one, the neural network’s task is to offer individuals with greater potential based on the reconstructed search path information.

Regarding the loss function, mean squared error (MSE) is generally considered a reasonable option. However, we observed that network reconstruction faces significant challenges when relying on MSE. In the context of fitting with the MSE loss function, the loss in a network arises from the mean square error between the network predictions and the actual labels. This implies that the network assigns equal importance to all data points, leading to significant consumption of computational resources and frequently failing to attain an optimal fit. During the process of training the network for reconstruction, our expectation is for the network to focus on regions with better fitness quality while minimizing attention to areas with poor solution quality. Therefore, we adopt a newly designed fitness mean squared error (FMSE) loss function, the formula for which is presented below:

$$
l o s s = \frac { 1 } { D } \sum _ { i = 1 } ^ { D } ( | y _ { i } - \hat { y _ { i } } | ^ { 2 } + f _ { i } ) ,
$$

where $f _ { i }$ represents the fitness value corresponding to the $i \cdot$ -th individual. The FMSE incorporates fitness into the computation, directing the network’s attention toward regions with higher solution quality. By assigning lower loss values to regions with higher qualitative, the FMSE encourages the network to engage in more refined learning and optimization in these areas. The adoption of the FMSE allows the network to focus more on solution spaces with more promising fitness areas, thereby enhancing reconstruction performance and reducing computational resource consumption. This approach provides a novel pathway for achieving more efficient network training and better fitting within solution spaces. The construction of FMSE and the integration of fitness into the loss function are discussed in greater detail in Supplementary File Section III.B. The network reconstruction pseudocode is presented in Algorithm 2.

Algorithm 2: Network Reconstruction   
Algorithm 3: Hybrid Exploitation   

<html><body><table><tr><td>Input: Dataset, fbest Output: A network-fitted optimal solution y 1:Randomly initialize network parameters; 2: for e from O to Epoch do 3: for pair in Dataset do</td></tr><tr><td>4: f,y←pair;</td></tr><tr><td>5: y = network(f); 6: calculate loss by (6);</td></tr><tr><td>7: update the network's parameters by loss;</td></tr><tr><td>8: end for</td></tr><tr><td>9:end for 10:y* = network(fbest);</td></tr></table></body></html>

# E. Hybrid Exploitation

In the exploitation section, we adopt a hybrid sampling approach using CMAES combined with univariate sampling [38]. CMAES is particularly suitable for addressing unimodal problems, while single sampling is effective for handling multimodal problems. This combination combines multivariate sampling with univariate sampling to effectively enhance the algorithm’s search performance across a wide range of problem types. Initially, CMAES is utilized to optimize the network-fitted optimal solution $y ^ { * }$ , if multiple updates with CMAES yield no improvement in solutions, the execution of CMAES ceases and we obtain the optimal solution $y ^ { \prime }$ . The number of function evaluations consumed by CMAES is denoted as $F E s _ { C M A E S }$ . The hyperparameter settings used for CMAES can be found in Supplementary File Table S.VIII. Subsequently, $y ^ { \prime }$ is used to generate a new population, subsequently passed to univariate sampling for additional optimization. $y ^ { \prime }$ is used as the mean value for each dimension, and the variance is set to $1 . M$ individuals are randomly generated following a normal distribution to form the population $P ^ { \prime }$ , with the mean of each dimension corresponding to the value of $y ^ { \prime }$ in that dimension. For univariate sampling, the population is updated by selecting the top $N$ individuals to calculate the mean $( \mu _ { d } )$ and variance $( \sigma _ { d } )$ for each dimension, which are then used to update the population. In order to guarantee the convergence of the algorithm, we set the parameter $_ c c$ to control the $\sigma _ { d }$ , with $c c$ approaching but remaining below 1 $( c c  1 ^ { - } ) ,$ ) [39]. The exploitation pseudocode is presented in Algorithm 3.

Compared to traditional evolutionary algorithms, DLES exhibits significant advantages. The combination of evolutionary operators and deep learning allows these two methods to complement each other. In contrast to approaches that integrate deep learning, DLES possesses its own unique features. Many existing methods leverage deep learning to optimize algorithm hyperparameters or assist in selecting offspring. However,

nput: A network-fitted optimal solution $y ^ { * }$ , an evaluation function $F ( \cdot )$ , the maximum number of function evaluations $M F E s$ and $N F E s$ ;   
Output: A development solution $y ^ { \ast }$   
1: $y ^ { \prime } \gets \mathrm { C M A E S } ( y ^ { * } , F ( \cdot ) )$ ;   
2: $N F E s + = F E s _ { C M A E S }$ ; $/ /$ Add the number of function evaluations by CMAES   
3: $t = 0$ ;   
4: Generate a total population $P ^ { \prime }$ containing $M$ individuals based on $y ^ { \prime }$ , $M = 2 N$ ;   
5: while $N F E s < M F E s$ do   
6: select $N < M$ promising solutions $P ^ { t }$ sorted as $y _ { 1 }$ to $y _ { N }$ from $P ^ { \prime }$ , record the best solution $y _ { 1 }$ to $y _ { b e s t }$ ;   
7: Calculate the weighted mean value $\mu _ { d }$ and the standard deviation $\sigma _ { d }$ on each dimension $y _ { d }$ ;   
8: if $y _ { b e s t }$ no improvement in the latest $I$ iterations then   
9: reduce the search space by: $\sigma _ { d } = \sigma _ { d } \times c c ~ ( c c \to 1 ^ { - } )$ ;   
10: end if   
11: Sample $P ^ { t }$ to generate $G$ new solutions $P ^ { \prime }$ with variable $\sigma _ { d }$ and $\mu _ { d }$ ;   
12: $t + = 1$ ;   
13: $N F E s + = M$ ;   
14: end while   
15: $\mathcal { F }  F ( P ^ { t + 1 } )$ ;   
16: select $y ^ { * }$ from $\mathcal { F }$ ;//best individual

DLES utilizes neural networks to reconstruct the authentic search path. This approach not only enhances the algorithm’s understanding of authentic search spaces but also enables better utilization of information within the solution space, further improving algorithm performance.

# IV. Experiments and Analysis

To evaluate the DLES, we perform extensive experiments comparing it with fourteen leading algorithms, including both traditional evolutionary methods, those with deep learning integration and iterative surrogate-based methods. We explore network structure and hyperparameters to determine optimal settings for DLES. Ablation studies assess the impact of omitting network reconstruction, highlighting the benefits of this approach. Additionally, we analyze DLES’s role in population updating and begin to explore its complexity.

For a more comprehensive analysis, please refer to the Supplementary File: the network effectiveness analysis, the impact of different network structures, activation functions selection and discussion of the loss function, the performance of DLES on different types of problems, post-hoc tests in BBOB, and additional supplementary experimental results are given in Section II − VI of Supplementary File, respectively.

# A. Benchmark Problems and Performance Metrics

Ensuring fair comparisons among different algorithms and fostering consensus on algorithm performance is paramount, so we utilize the single-objective real-parameter numerical optimization competition of IEEE Congress on Evolutionary Computation CEC 2014 [40], CEC 2018 [41], CEC 2022 [42], and BBOB [43] comprising a total of 95 problems as our benchmark test set.

TABLE I Comparison scores and rankings of DLES and other competitors on CEC benchmarks.   

<html><body><table><tr><td colspan="2"></td><td colspan="3">Learning-based algorithm</td><td colspan="10">Traditional algorithm</td></tr><tr><td>BenchmarkDimension</td><td></td><td></td><td></td><td></td><td>DLESLEO-PSO QHSES BOBYQA COBYLA CMAES DDEARA EA4eigHSES</td><td></td><td></td><td></td><td></td><td></td><td></td><td>LSHADE TAPSO MLGSA</td><td></td><td>p-value</td></tr><tr><td rowspan="4">CEC 2014</td><td>D=10</td><td>4.6833</td><td>8.1667</td><td>4.7167</td><td>8.4167</td><td>9.5500</td><td>9.0000</td><td>8.1667</td><td>2.8500</td><td>4.6500</td><td>3.3000</td><td>6.9667</td><td>7.5333</td><td>1.37e-28</td></tr><tr><td>D=30</td><td>4.4500</td><td>5.1667</td><td>4.6833</td><td>7.8833</td><td>8.5000</td><td>8.2667</td><td>9.7333</td><td>4.4833</td><td>4.8500</td><td>4.1500</td><td>8.4333</td><td>7.4000</td><td>2.09e-30</td></tr><tr><td>D= 50</td><td>4.1000</td><td>4.4000</td><td>4.7500</td><td>7.4000</td><td>8.3667</td><td>8.0167</td><td>10.4333</td><td>5.0500 4.3333</td><td></td><td>5.4167</td><td>8.2000</td><td>7.5333</td><td>8.30e-28</td></tr><tr><td>D=100</td><td>3.9500</td><td>3.7333</td><td>4.9333</td><td>6.6500</td><td>7.8500</td><td>7.3667</td><td>10.4333</td><td>6.3167 4.7667</td><td></td><td>5.6667</td><td>8.4167</td><td>7.9167</td><td>7.08e-30</td></tr><tr><td rowspan="4">CEC 2018</td><td>D=10</td><td>4.6379</td><td>9.0345</td><td>9.6552</td><td>8.5517</td><td>5.4655</td><td>9.0000</td><td>3.6897</td><td>2.5517</td><td>5.4655</td><td>4.0690</td><td>7.8448</td><td>8.0345</td><td>6.78e-27</td></tr><tr><td>D = 30</td><td>3.1034</td><td>8.1379</td><td>9.6552</td><td>9.9310</td><td>4.0690</td><td>8.6724</td><td>6.9138</td><td>4.1897</td><td>3.4655</td><td>3.6207</td><td>8.1724</td><td>8.0690</td><td>6.65e-23</td></tr><tr><td>D=50</td><td>2.9483</td><td>8.0862</td><td>9.6034</td><td>9.7586</td><td>3.3103</td><td>7.9310</td><td>7.5862</td><td>4.7586</td><td>3.4138</td><td>3.9483</td><td>8.1034</td><td>8.5517</td><td>1.27e-21</td></tr><tr><td>D=100</td><td>2.5172</td><td>7.9828</td><td>8.9138</td><td>9.9310</td><td>2.6897</td><td>7.5345</td><td>8.3276</td><td>5.6207</td><td>2.8276</td><td>4.8276</td><td>8.2069</td><td>8.6207</td><td>2.54e-21</td></tr><tr><td rowspan="2">CEC 2022</td><td>D=10</td><td>3.0000</td><td>11.0833</td><td>3.4167</td><td>5.1667</td><td>5.1667</td><td>6.0833</td><td>9.5833</td><td>2.4167</td><td>3.5000</td><td>9.7500</td><td>7.8333</td><td>11.0000</td><td>2.77e-15</td></tr><tr><td>D= 20</td><td></td><td>9.9167</td><td>3.3750</td><td>6.2917</td><td>6.2917</td><td>6.0833</td><td>9.4167</td><td>2.4583</td><td>3.4583</td><td>9.8333</td><td>7.1667</td><td>10.5000</td><td>3.68e-14</td></tr><tr><td colspan="2">Average Score</td><td>3.2083</td><td></td><td>8.753</td><td></td><td></td><td>8.240</td><td></td><td>5.240</td><td></td><td>4.400</td><td></td><td>7.788</td><td></td></tr><tr><td colspan="2">Total Ranking</td><td>3.699 1</td><td>7.901 10</td><td>5</td><td>7.130 9</td><td>4.444 8</td><td>4</td><td>7.267 7</td><td>12</td><td>4.551 2</td><td>11</td><td>8.583 6</td><td>3</td><td></td></tr></table></body></html>

TABLE II Comparison scores and rankings of DLES and other competitors on bbob benchmark.   

<html><body><table><tr><td></td><td>DLES</td><td>a-CMAES</td><td>sc-CAMES</td><td>CMAES</td><td>HSES</td><td>E-WOA</td></tr><tr><td>D=2</td><td>1.5833</td><td>4.3958</td><td>3.2292</td><td>3.2708</td><td>4.5625</td><td>3.9583</td></tr><tr><td>D=3</td><td>1.4583</td><td>3.4792</td><td>3.1042</td><td>3.3542</td><td>5.1458</td><td>4.4583</td></tr><tr><td>D=5</td><td>1.7917</td><td>2.9375</td><td>3.1458</td><td>2.8125</td><td>5.5208</td><td>4.7917</td></tr><tr><td>D=10</td><td>1.8125</td><td>3.0208</td><td>3.0625</td><td>2.8958</td><td>5.6042</td><td>4.6042</td></tr><tr><td>D=20</td><td>2.0208</td><td>2.5625</td><td>3.1458</td><td>2.8542</td><td>5.7292</td><td>4.6875</td></tr><tr><td>D=40</td><td>1.9375</td><td>2.2500</td><td>3.0417</td><td>3.2083</td><td>5.8333</td><td>4.7292</td></tr><tr><td>Average Score</td><td>1.7660</td><td>3.1076</td><td>3.2881</td><td>3.0659</td><td>5.3993</td><td>4.5382</td></tr><tr><td>Total Ranking</td><td>1</td><td>4</td><td>2</td><td>3</td><td>6</td><td>5</td></tr></table></body></html>

The CEC benchmark test set of optimization problems covers a range of types, including unimodal, multimodal, hybrid, and more intricate hybrid problems. The bounds for each dimension are uniformly set between $- 1 0 0$ and 100. In detail, CEC 2014 presents 30 distinct problems, while CEC 2018 contributes 29 problems. The test instances are further categorized into dimensions of 10, 30, 50, and 100. For these problems, the maximum number of function evaluations $( M F E s )$ is set at $1 0 , 0 0 0 \times D$ , where $D$ represents the problem dimension. Moving on to CEC 2022, which introduces 12 problems categorized into dimensions of 10 and 20, the MFEs are set at 200,000 for $D = 1 0$ and 1,000,000 for $D = 2 0$ . According to the CEC official guidelines, results less than $1 0 ^ { - 8 }$ are recorded as 0, indicating that the algorithm successfully finds the global optimum.

The BBOB benchmark set includes a diverse array of optimization problems, specifically designed to assess algorithm performance across various complexities. Unlike the CEC benchmarks, the maximum number of function evaluations MFEs in BBOB is set $1 0 ^ { 5 }$ for all problem instances. The BBOB set comprises 24 problems, evaluated across dimensions of 2, 3, 5, 10, 20, and 40. This standardized evaluation limit ensures consistency in comparing algorithm performance across all problems and dimensions within the BBOB suite. On the BBOB set, we have retained the full precision of the values without truncating to zero.

To comprehensively compare the performance of different hyperparameters and network structures across the problem set, we employ the Friedman rank-sum test [44] to rank the experimental results. For better validation of DLES performance, we utilize the Friedman rank-sum test to compute scores and rankings for the experimental results across all methods. We aggregate the performance by taking the average of the final results from multiple experimental runs before ranking, ensuring a robust comparison across different methods. This approach allows us to rank the performance of different hyperparameter configurations, thereby identifying the optimal hyperparameter settings. This method comprehensively considers multiple factors, providing relatively objective ranking results to assist us in making more informed decisions.

# B. Experimental Setup

Empirically validating the effectiveness of our proposed method involves a comparison with fourteen competitive algorithms, including classic CMAES [45], the improved versions of CMA-ES (a-CMAES and sc-CMAES [46]), the champion of CEC 2014 (LSHADE [47]), the champion of CEC 2018 (HSES [39]), the champion of CEC 2022 (EA4eig [42]), three widely adopted algorithms E-WOA [48], MLGSA [35], DDEARA [49], TAPSO [50]. Additionally, we included two algorithms combining deep learning (QHSES [23] and LEO-PSO [26]) and two iterative surrogate-based methods (BOBYQA [50] and COBYLA [50]). In DLES, hyperparameter configurations are as follows: number of updates to the population during the exploration process $T \ = \ 1 0 0$ , network learning rate $\mathrm { ( l r ) } ~ = ~ 0 . 5$ , and the epoch is set to 10. The hyperparameter settings for each compared algorithm remain consistent with their respective original papers, detailed configurations can be found in the Supplementary File Table S.VIII.

This experimental workstation is equipped with Pytorch on a PC with an AMD Ryzen 7 1700X $@$ 3.4GHz, eight-core processor and 16GB RAM, using Ubuntu 18.04.6 LTS OS. Each algorithm is independently run for 51 times on each function to minimize variability and ensure the attainment of robust and consistent outcomes.

# C. Effectiveness of Deep Learning-based Search Path Reconstruction Evolution Strategy

In Table I, we present the score results and final rankings of DLES and other methods on the benchmark. For each problem set, the Friedman rank-sum test is employed to calculate the algorithm’s score on that specific set of problems. This score is then divided by the total number of problems to obtain the algorithm’s average score across all problems within the current problem set. The average scores for all problem sets are summed up to derive the algorithm’s final score across all problem sets. Additionally, we have included the p-values from the Friedman rank-sum test in the table for reference. The small p-values indicate significant differences in performance between the various algorithms. It is evident that DLES outperforms all comparative algorithms through the scores and ranking table. The specific results of the mean and standard deviation for the 51 experiments are detailed in Tables S.IX − S.XVIII of Supplementary File. In the tables, we have bolded the best value for each problem. We also present the p-value tests of the statistical significance analysis, with the detailed results available in the Supplementary File Tables S.XIX to S.XXVIII. In the tables, we have bolded the p-values less than 0.001, which typically indicate a highly significant statistical difference.

In Table I, the performance of DLES across diverse test sets is distinctly depicted. Compared to EA4eig and LSHADE, DLES exhibited slightly diminished scores in dimensions 10 and 30 of CEC 2014. However, it significantly outperforms these algorithms in $\textit { D } = \ 5 0$ , achieving the highest scores among the compared methods. Additionally, in $D = 1 0 0$ DLES exhibited exceptional performance, ranking second only to LEO-PSO. Compared to DDEARA, EA4eig, and LSHADE, DLES records lower scores in dimension 10 of CEC 2018. However, it showcases exemplary performance in dimensions 30, 50, and 100, thereby securing the foremost position once again. In the CEC 2022, DLES scores marginally below the leading algorithm DDEARA, ultimately securing the second position.

The performance of DLES across various test sets underscores its overall effectiveness in tackling diverse types of problems. While it may fall behind certain algorithms in specific low-dimensional scenarios, its consistently strong results showcase its ability to handle a broad spectrum of problems with remarkable proficiency.

Table II presents the comparison scores and rankings of DLES against other competing algorithms on the BBOB benchmark across various dimensions. DLES consistently outperforms the other algorithms, underscoring its significant advantage in solving BBOB optimization problems. The detailed post-hoc test results and analysis for DLES and the other comparison algorithms on the BBOB can be found in the Supplementary File Section V. Overall, DLES achieves superior performance among all tested methods, demonstrating its effectiveness and robustness across different problem dimensions.

# D. Discussion of Network Structure and Hyperparameters

We explore the architecture of neural networks and devise a series of network models for experimentation. To simplify the models and avoid the use of complex neural network structures, we employed only fully connected layers to delve into the impact of different widths and depths on the final reconstruction capability of the network. While maintaining consistency in the input and output layers, we designed the following five network structures:

TABLE III Friedman ranking of DLES across various combinations of network structures and loss functions.   

<html><body><table><tr><td>Net</td><td>Loss</td><td>rank</td><td>Net</td><td>Loss</td><td>rank</td></tr><tr><td>Net1</td><td>MSE</td><td>11</td><td>Net1</td><td>FMSE</td><td>9</td></tr><tr><td>Net2</td><td>MSE</td><td>8</td><td>Net2</td><td>FMSE</td><td>10</td></tr><tr><td>Net3</td><td>MSE</td><td>2</td><td>Net3</td><td>FMSE</td><td>1</td></tr><tr><td>Net4</td><td>MSE</td><td>3</td><td>Net4</td><td>FMSE</td><td>4</td></tr><tr><td>Net5</td><td>MSE</td><td>7</td><td>Net5</td><td>FMSE</td><td>5</td></tr><tr><td>None</td><td>None</td><td>6</td><td></td><td></td><td></td></tr></table></body></html>

1) Net1: $1 { - } D$ ,   
2) Net2: 1-2D-D,   
3) Net3: 1-2D-4D-D,   
4) Net4: 1-2D-4D-2D-D,   
5) Net5: 1-2D-4D-8D-D.

Net1 is a network without the hidden layer. Net2 Net5 represent the network with a different number of hidden layers, where 2D, 4D, and $8 D$ denote the size of each hidden layer. Simultaneously, to assess the performance of the proposed loss function, we compare the performance of the conventional MSE loss function with the proposed FMSE loss function across different network structures.

In Table III, we present combinations of various network structures and loss functions, ranked according to the experimental results using the Friedman test to reflect their relative performance, where “None” means a variant without using deep learning. These experimental results indicate that the proposed FMSE loss function exhibits superior performance in network fitting. Additionally, different network structures also influence the performance of the loss function to some extent. By comparing the experimental results, we observe that the Net3 structure demonstrates superior performance when combined with the FMSE loss function.

We conduct the hyperparameters of DLES, involving discussions on the sampling iteration count in exploration $( T )$ , lr, and epoch. Experimental results for various combinations of hyperparameters are computed using the Friedman ranksum test, and the corresponding scores are ranked, with lower scores indicating higher ranks. Table IV presents the experimental results, revealing that the optimal hyperparameter combination is $T = 1 0 0$ , $\mathrm { l r } = 0 . 5$ , and the epoch is set to 10. Additionally, the scores obtained through the rank-sum test indicate minimal differences in the final scores among different hyperparameter combinations, suggesting that the performance gap between these combinations is small. This observation implies that our proposed method is insensitive to hyperparameter variations, showcasing robustness. Consequently, our method demonstrates strong robustness when confronted with diverse hyperparameter settings, contributing to improved stability and reliability.

To better demonstrate the impact of different network structures on the DLES, we provide additional analyses of neural structure in the Supplementary File Section III. These experiments compare the performance of the algorithm using Net1-Net5 networks versus not using any network (NoNet) across various types of problems. Overall, networkbased fitting consistently outperforms cases without networks. Typically, as problem dimensionality and complexity increase, more complex networks tend to deliver better performance. While selecting different networks for specific problems could yield better results, such an approach would undermine the generality and scalability of the algorithm. Tailoring networks for individual problems increases complexity and reduces the robustness of the solution, which goes against the broader goal of developing a more universally applicable optimization method. Therefore, we choose Net3, which performs well across most problems, as the base network for DLES.

TABLE IV Friedman ranking of DLES under different hyperparameter combinations.   

<html><body><table><tr><td>T</td><td>lr</td><td>epoch</td><td>score</td><td>rank</td><td>T</td><td>lr</td><td>epoch</td><td>score</td><td>rank</td></tr><tr><td>50</td><td>0.1</td><td>10</td><td>431.5</td><td>19</td><td>100</td><td>0.5</td><td>50</td><td>329</td><td>16</td></tr><tr><td>50</td><td>0.5</td><td>10</td><td>427</td><td>18</td><td>100</td><td>0.5</td><td>100</td><td>313.5</td><td>14</td></tr><tr><td>50</td><td>1.0</td><td>10</td><td>445</td><td>20</td><td>100</td><td>1.0</td><td>10</td><td>309</td><td>13</td></tr><tr><td>100</td><td>0.01</td><td>10</td><td>275</td><td>9</td><td>200</td><td>0.1</td><td>10</td><td>241.5</td><td>2</td></tr><tr><td>100</td><td>0.01</td><td>50</td><td>355</td><td>17</td><td>200</td><td>0.1</td><td>50</td><td>256.5</td><td>6</td></tr><tr><td>100</td><td>0.01</td><td>100</td><td>325</td><td>15</td><td>200</td><td>0.1</td><td>100</td><td>269.5</td><td>8</td></tr><tr><td>100</td><td>0.1</td><td>10</td><td>286.5</td><td>10</td><td>200</td><td>0.5</td><td>10</td><td>259</td><td>7</td></tr><tr><td>100</td><td>0.1</td><td>50</td><td>291</td><td>11</td><td>200</td><td>0.5</td><td>50</td><td>251.5</td><td>5</td></tr><tr><td>100</td><td>0.1</td><td>100</td><td>298</td><td>12</td><td>200</td><td>0.5</td><td>100</td><td>246.5</td><td>3</td></tr><tr><td>100</td><td>0.5</td><td>10</td><td>231</td><td>1</td><td>200</td><td>1.0</td><td>10</td><td>249</td><td>4</td></tr></table></body></html>

# E. Algorithm Analysis of Exploration and Exploitation

In the aforementioned discussion of hyperparameters, we determine the optimal exploration sampling count as $T \ =$ 100. In alignment with the concept of DLES, we aim for the neural network to reconstruct the exploration paths of the algorithm within the solution space. Therefore, we employ a dimension-wise diversity-based method [51] to gauge the relationship between exploration and exploitation. This relationship is measured in terms of the percentage of exploration and exploitation, and the equation can be expressed by the following:

$$
X P L = \left( \frac { D i \nu } { D i \nu _ { m a x } } \right) ,
$$

$$
X P T = 1 - X P L ,
$$

where XPL and XPT represent the level of exploration and exploitation, respectively. $D i \nu _ { m a x }$ is the maximum distance of all $D i \nu$ in the current iteration.

$$
D i \nu = \frac { 1 } { D } \sum _ { j = 1 } ^ { D } D i \nu _ { j } ,
$$

$$
D i \nu _ { j } = \frac { 1 } { N } \sum _ { i = 1 } ^ { N } \left| x _ { i } ^ { j } - m e d i a n ( x ^ { j } ) \right| ,
$$

where $x _ { i } ^ { j }$ denotes the jth dimension of the ith individual. $N$ and $D$ represent the population and dimension size, respectively. median $( x ^ { j } )$ represents the median value of the $j \mathrm { t h }$ dimension in the entire population.

Exploring and developing proportions within a population can reveal the current state of the population. In Fig. 3, we select representatives F2, F6, F15, and F25 to illustrate the population dynamics of DLES during the search process in the solution space on $D = 3 0$ . The dashed vertical line in the figure corresponds to when DLES utilizes the network for reconstruction. The left side of the dashed line represents the exploration and sampling phase of our proposed DLES, while the right side represents the subsequent development phase. As the population state remains nearly constant during the subsequent development process, only the population state at the first 50,000 evaluations is displayed.

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/ab99c5d3342b0e2f976b88068894d2ffbbfb2fb9aaaf3885ae17cc0c1ffeb5be.jpg)  
Fig. 3. Exploration and exploitation percentage of DLES for F2, F6, F15, and F25 with 30 dimensions.

From Fig. 3, it is evident that when DLES employs the network for simulating the solution space, XPL’s value approaches 0, XPT one approaches 1. The population has almost entirely transitioned to the development phase, signifying the conclusion of the exploration process. The data provided to train the network for reconstruction corresponds to the population’s exploration path in the solution space, aligning precisely with the original intent of our algorithm design.

The quantity and quality of training samples are crucial for effective network learning; hence, ensuring high-quality adequate training sample is of paramount importance. In terms of sample quantity, the quantity is controlled by the optimal exploration sampling count $T$ . Theoretically, increasing the sampling iterations deepens the understanding of the solution space. A higher $T$ value results in more training samples, which enhances the network’s performance. However, due to the limited number of evaluations, excessive allocation of evaluations to the exploration phase can lead to insufficient evaluations for subsequent algorithm exploitation. Therefore, a careful balance of $T$ is required. Table IV discusses the impact of various $T$ . It can be observed that, with consistent network training parameters, the algorithm achieves the best overall performance when $T$ is equal to 100. If the sampling iterations are too few, the network struggles to learn effectively; if too many, it adversely affects the subsequent exploitation phase of the algorithm.

Regarding sample quality, we indirectly ensure the quality of training samples by measuring the exploration-exploitation ratio of the population. When $T$ is equal to 100, the evaluations consumed amount to 2000. As shown in Fig. 3, the population stabilizes at the same level, primarily remaining in the exploitation phase, indicating that the algorithm has largely completed its exploration phase. This stability ensures the quality of our training samples.

TABLE V Total runtime for a average execution on CEC 2018 in $D = 3 0$ .   

<html><body><table><tr><td></td><td>D=10</td><td>D=30</td><td>D=50</td><td>D=100</td><td>Total time</td><td>Growth rate</td></tr><tr><td>NO Net</td><td>45.7292</td><td>330.2130</td><td>650.3522</td><td>2581.5094</td><td>3607.8038</td><td>1</td></tr><tr><td>IN CPU</td><td>87.3057</td><td>428.7092</td><td>768.8923</td><td>2813.0812</td><td>4098.9884</td><td>13.6%</td></tr><tr><td>IN GPU</td><td>162.1032</td><td>498.2178</td><td>792.2512</td><td>2703.7251</td><td>4156.2973</td><td>15.2%</td></tr></table></body></html>

# F. Complexity Analysis

DLES does not entail supplementary evaluation costs in its utilization of the network for learning and fitting. Consequently, it avoids incurring additional expenses in situations where constraints exist on the number of evaluations, rendering it particularly advantageous for problems characterized by limited evaluation resources.

In terms of space complexity, DLES differs from other algorithms as it necessitates additional storage for the dataset used in network training. However, the proportion of this data relative to the overall evaluation dataset is $1 / K$ , where $K$ is a parameter associated with the hyperparameter of the sampling iterations $T$ , the value of $1 / K$ is exceptionally low at 0.0776. Therefore, the increase in space complexity for DLES due to network augmentation does not exceed $O ( N \cdot D )$ . This implies that, in comparison to other algorithms, DLES does not exhibit a significant increase in overall space utilization.

The DLES’s time complexity is closely tied to the training of the neural network. Specifically, the increased time is primarily consumed during the process of network training. The time consumption for network training is associated with the number of training epochs. The time complexity of the DLES algorithm is expressed as $O ( n ) = T \cdot O ( N \cdot D ) + E \cdot \alpha \cdot T \cdot$ · $O ( N \cdot D ) + ( G - T ) \cdot O ( N ^ { 2 } \cdot D )$ , where $T$ is the number of updates to the population during the exploration process, $G$ is the total number of iterations, $E$ is the number of training epochs for the network, $\alpha { \cdot } T$ represents the number of iterations in each epoch and $\alpha$ belongs to the interval (0,1]. For our recommended parameter setting of the epoch is 10, the network increases time consumption does not exceed the $O ( N \cdot D )$ range. It is essential to highlight that leveraging GPU acceleration during the neural network training process can further diminish the time required for training.

In Table $\mathrm { \Delta V }$ , we depict the time consumption for a average run on the CEC 2018 problem set under scenarios without network, with network training using CPU, and with network training using GPU. In Table V, we observe that the use of network training only increases the overall time consumption by approximately $1 3 \small { \sim } 1 5 \%$ . However, it is noteworthy that the total time consumption with GPU acceleration is slightly higher than that with CPU usage. We hypothesize that this might be attributed to the time required for data transfer from CPU to GPU, particularly evident in scenarios with smaller problem dimensions and lesser data volume. In such cases, the data transfer process constitutes a significant portion of the overall time. As the problem dimensions increase, the proportion of time spent on data transfer gradually diminishes. The data in Table V largely aligns with our conjecture, in the case of $D = 1 0 0$ , the GPU training accelerates the process.

In summary, DLES excels in complexity analysis, circumventing additional evaluation costs, ensuring effective space utilization, and enabling GPU acceleration for relatively higher dimensional problems.

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/ea952e3f875943f3b395cadfed20b970e97b34e71b520be480c4a725d88600f8.jpg)  
Fig. 4. Visualization of the exploration process of DLES algorithm on CEC 2018 F27.

# G. Visualization of Reconstructed Exploration Path

To better demonstrate the process and advantages of our proposed algorithm in reconstructing the solution space. We provide a two-dimensional visualization of the DLES algorithm’s exploration process on CEC 2018 F27. As illustrated in Fig. 4, the red regions represent potential areas, while the white pentagrams denote the true optimal points. The yellow crosses indicate the sampling locations during the algorithm’s exploration within the search space. The green hexagons show the positions of the solutions with higher potential provided after network reconstruction. The colors in the figure represent the fitness values, with darker colors indicating smaller fitness values. The color bar on the right side of the figure shows the specific fitness values corresponding to different colors in this problem. For the F27 problem, the optimal fitness value is 2800.

The yellow cross marks in Fig. 4 represent the positions sampled by the algorithm during the exploration process. At regular intervals during the iterations, the current population’s position is displayed. The yellow arrows, derived from the iteration process, show the directional movement of the population in the solution space as iterations progress. The white curve represents the reconstructed search path after the network’s learning process. By inputting specified fitness values, the network returns the corresponding positions, which are then plotted in the solution space. During the algorithm’s iterative exploration, the population’s movement is directional, which is why yellow arrows are used to indicate this movement. After the network reconstructs the search path, its understanding of the search space becomes continuous, which is why the white curve is used to represent it.

The visualization reveals that the algorithm is misled and gradually converges towards a local optimum. However, the network’s reconstruction of the solution space corrected this issue, allowing the algorithm to subsequently explore and ultimately find the true optimal solution.

# V. Conclusion

In this paper, we propose a deep learning-based search path reconstruction evolution strategy algorithm (DLES) for reconstructing the authentic solution space. The algorithm leverages a neural network to model exploration paths derived from the sampling results of evolutionary operators within the solution space. By doing so, the network identifies individuals with greater potential along the exploration path, guiding further search through a hybrid sampling approach. Unlike traditional evolutionary operator sampling methods, this network-based reconstruction offers a deeper understanding of the solution space, reducing the risk of local optima in complex problems. As a result, DLES more efficiently explores the solution space, uncovering promising solutions. To assess DLES’s performance, we compare it to top algorithms on CEC and BBOB benchmarks, where it shows superior results. Experiments reveal that DLES is robust to hyperparameter changes, maintaining strong stability and generalization. We also examine the population state and the effects of network reconstruction, confirming DLES’s ability to find more promising solutions. Finally, we analyze DLES’s complexity, showing that the added time and space costs from network reconstruction are minimal, with no extra evaluation counts needed.

DLES gathers crucial insights from the entire search process by fitting exploration paths. While modeling solution spaces by using neural networks is not new, previous methods often perform poorly because they attempt to fit the entire solution space, which results in excessive network complexity. By focusing solely on modeling exploration paths, we significantly reduce the network’s load, allowing even simpler architectures to handle complex tasks. The incorporation of neural networks, with their powerful nonlinear fitting capabilities, enables DLES to combine the strengths of both networkdriven and algorithm-driven approaches, highlighting the value of integrating deep learning with evolutionary algorithms in optimization challenges.

In the future, we plan to extend the solution space reconstruction method to various aspects. Firstly, this paper primarily focuses on single-objective problems, and we hope to extend the DLES method to multi-objective problems. Secondly, by exploring and further developing more powerful evolutionary operators and strategies, we hope to enhance the potential of DLES. Finally, in this study, we only reconstructed the exploratory part of the solution space. In the future, we will use more robust networks to reconstruct the more true exploration space. These extensions will contribute to further advancing the development of the DLES algorithm, providing more possibilities for its practical applications in real-world problems.

# References

[1] A. E. Eiben and J. Smith, “From evolutionary computation to the evolution of things,” Nature, vol. 521, no. 7553, pp. 476–482, 2015.   
[2] B. Li, Z. Wei, J. Wu, S. Yu, T. Zhang, C. Zhu, D. Zheng, W. Guo, C. Zhao, and J. Zhang, “Machine learning-enabled globally guaranteed evolutionary computation,” Nature Machine Intelligence, pp. 1–11, 2023.   
[3] S. Gao, Y. Yu, Y. Wang, J. Wang, J. Cheng, and M. Zhou, “Chaotic local search-based differential evolution algorithms for optimization,” IEEE Transactions on Systems, Man and Cybernetics: Systems, vol. 51, no. 6, pp. 3954–3967, 2021.   
[4] D. A. Boiko, R. MacKnight, B. Kline, and G. Gomes, “Autonomous chemical research with large language models,” Nature, vol. 624, no. 7992, pp. 570–578, 2023.   
[5] M. F. Degenhardt, H. F. Degenhardt, Y. R. Bhandari, Y.-T. Lee, J. Ding, P. Yu, W. F. Heinz, J. R. Stagno, C. D. Schwieters, N. R. Watts et al., “Determining structures of RNA conformers using AFM and deep neural networks,” Nature, pp. 1–10, 2024. [6] S. Dong, P. Wang, and K. Abbas, “A survey on deep learning and its applications,” Computer Science Review, vol. 40, p. 100379, 2021. [7] C. Lee, H. Hasegawa, and S. Gao, “Complex-valued neural networks: A comprehensive survey,” IEEE/CAA Journal of Automatica Sinica, vol. 9, no. 8, p. 1406–1426, 2022.   
[8] W. Ding, W. Pedrycz, G. G. Yen, and B. Xue, “Guest editorial evolutionary computation meets deep learning,” IEEE Transactions on Evolutionary Computation, vol. 25, no. 5, pp. 810–814, 2021. [9] G. Ma, Z. Wang, W. Liu, J. Fang, Y. Zhang, H. Ding, and Y. Yuan, “Estimating the state of health for lithium-ion batteries: A particle swarm optimization-assisted deep domain adaptation approach,” IEEE/CAA Journal of Automatica Sinica, vol. 10, no. 7, pp. 1530–1543, 2023.   
[10] Z. Lei, S. Gao, Z. Zhang, H. Yang, and H. Li, “A chaotic local searchbased particle swarm optimizer for large-scale complex wind farm layout optimization,” IEEE/CAA Journal of Automatica Sinica, vol. 10, no. 5, pp. 1168–1180, 2023.   
[11] Y. Yu, Z. Lei, Y. Wang, T. Zhang, C. Peng, and S. Gao, “Improving dendritic neuron model with dynamic scale-free network-based differential evolution,” IEEE/CAA Journal of Automatica Sinica, vol. 9, no. 1, pp. 99–110, 2022.   
[12] E. Galv´an and P. Mooney, “Neuroevolution in deep neural networks: Current trends and future challenges,” IEEE Transactions on Artificial Intelligence, vol. 2, no. 6, pp. 476–493, 2021.   
[13] J.-Y. Li, Z.-H. Zhan, J. Xu, S. Kwong, and J. Zhang, “Surrogate-assisted hybrid-model estimation of distribution algorithm for mixed-variable hyperparameters optimization in convolutional neural networks,” IEEE Transactions on Neural Networks and Learning Systems, vol. 34, no. 5, pp. 2338–2352, 2023.   
[14] C. He, Y. Zhang, D. Gong, and X. Ji, “A review of surrogate-assisted evolutionary algorithms for expensive optimization problems,” Expert Systems with Applications, vol. 217, p. 119495, 2023.   
[15] N. Li, L. Ma, G. Yu, B. Xue, M. Zhang, and Y. Jin, “Survey on evolutionary deep learning: Principles, algorithms, applications, and open issues,” ACM Computing Surveys, vol. 56, no. 2, pp. 1–34, 2023.   
[16] M. Xu, Y. Mei, F. Zhang, and M. Zhang, “Genetic programming for dynamic flexible job shop scheduling: Evolution with single individuals and ensembles,” IEEE Transactions on Evolutionary Computation, 2023, doi: 10.1109/TEVC.2023.3334626.   
[17] L. Si, X. Zhang, Y. Tian, S. Yang, L. Zhang, and Y. Jin, “Linear subspace surrogate modeling for large-scale expensive single/multi-objective optimization,” IEEE Transactions on Evolutionary Computation, 2023, doi: 10.1109/TEVC.2023.3319640.   
[18] K. C. Tan, L. Feng, and M. Jiang, “Evolutionary transfer optimization - a new frontier in evolutionary computation research,” IEEE Computational Intelligence Magazine, vol. 16, no. 1, pp. 22–33, 2021.   
[19] M. Jiang, Z. Wang, L. Qiu, S. Guo, X. Gao, and K. C. Tan, “A fast dynamic evolutionary multiobjective algorithm via manifold transfer learning,” IEEE Transactions on Cybernetics, vol. 51, no. 7, pp. 3417– 3428, 2021.   
[20] K. Qiao, J. Liang, Z. Liu, K. Yu, C. Yue, and B. Qu, “Evolutionary multitasking with global and local auxiliary tasks for constrained multi-objective optimization,” IEEE/CAA Journal of Automatica Sinica, vol. 10, no. 10, pp. 1951–1964, 2023.   
[21] Z. Zhu, K. Lin, A. K. Jain, and J. Zhou, “Transfer learning in deep reinforcement learning: A survey,” IEEE Transactions on Pattern Analysis and Machine Intelligence, vol. 45, no. 11, pp. 13 344–13 362, 2023.   
[22] J. Sun, X. Liu, T. B¨ack, and Z. Xu, “Learning adaptive differential evolution algorithm from optimization experiences by policy gradient,” IEEE Transactions on Evolutionary Computation, vol. 25, no. 4, pp. 666–680, 2021.   
[23] H. Zhang, J. Sun, T. B¨ack, Q. Zhang, and Z. Xu, “Controlling sequential hybrid evolutionary algorithm by Q-Learning,” IEEE Computational Intelligence Magazine, vol. 18, no. 1, pp. 84–103, 2023.   
[24] P. Chrabaszcz, I. Loshchilov, and F. Hutter, “Back to basics: benchmarking canonical evolution strategies for playing atari,” in Proceedings of the 27th International Joint Conference on Artificial Intelligence (IJCAI). IEEE, 2018, pp. 1419–1426.   
[25] G. Ismayilov and H. R. Topcuoglu, “Neural network based multiobjective evolutionary algorithm for dynamic workflow scheduling in for optimization,” IEEE Transactions on Evolutionary Computation, vol. 27, no. 6, pp. 1794–1808, 2023.   
[27] X. Luo and L. O. Oyedele, “Forecasting building energy consumption: Adaptive long-short term memory neural networks driven by genetic algorithm,” Advanced Engineering Informatics, vol. 50, p. 101357, 2021.   
[28] E. Ayan, H. Erbay, and F. Varc¸ın, “Crop pest classification with a genetic algorithm-based weighted ensemble of deep convolutional neural networks,” Computers and Electronics in Agriculture, vol. 179, p. 105809, 2020.   
[29] E. Galv´an and P. Mooney, “Neuroevolution in deep neural networks: Current trends and future challenges,” IEEE Transactions on Artificial Intelligence, vol. 2, no. 6, pp. 476–493, 2021.   
[30] X. Liu, J. Sun, Q. Zhang, Z. Wang, and Z. Xu, “Learning to learn evolutionary algorithm: A learnable differential evolution,” IEEE Transactions on Emerging Topics in Computational Intelligence, vol. 7, no. 6, pp. 1605–1620, 2023.   
[31] M. Cui, L. Li, M. Zhou, J. Li, A. Abusorrah, and K. Sedraoui, “A Bi-population Cooperative Optimization Algorithm Assisted by an Autoencoder for Medium-scale Expensive Problems,” IEEE/CAA Journal of Automatica Sinica, vol. 9, no. 11, pp. 1952–1966, 2022.   
[32] M. Cui, L. Li, M. Zhou, and A. Abusorrah, “Surrogate-assisted autoencoder-embedded evolutionary optimization algorithm to solve high-dimensional expensive problems,” IEEE Transactions on Evolutionary Computation, vol. 26, no. 4, pp. 676–689, 2022.   
[33] Y. Liu, J. Liu, and S. Tan, “Decision space partition based surrogateassisted evolutionary algorithm for expensive optimization,” Expert Systems with Applications, vol. 214, p. 119075, 2023.   
[34] Q. Fan and O. K. Ersoy, “Zoning search with adaptive resource allocating method for balanced and imbalanced multimodal multi-objective optimization,” IEEE/CAA Journal of Automatica Sinica, vol. 8, no. 6, pp. 1163–1176, 2021.   
[35] Y. Wang, S. Gao, M. Zhou, and Y. Yu, “A multi-layered gravitational search algorithm for function optimization and real-world problems,” IEEE/CAA Journal of Automatica Sinica, vol. 8, no. 1, pp. 94–109, 2021.   
[36] M. Zhou, M. Cui, D. Xu, S. Zhu, Z. Zhao, and A. Abusorrah, “Evolutionary optimization methods for high-dimensional expensive problems: A survey,” IEEE/CAA Journal of Automatica Sinica, vol. 11, no. 5, pp. 1092–1105, 2024.   
[37] P. Larra˜naga and C. Bielza, “Estimation of Distribution Algorithms in Machine Learning: A Survey,” IEEE Transactions on Evolutionary Computation, 2023, doi: 10.1109/TEVC.2023.3314105.   
[38] Y. Fu and H. Wang, “A univariate marginal distribution resampling differential evolution algorithm with multi-mutation strategy,” in 2019 IEEE Congress on Evolutionary Computation (CEC). IEEE, 2019, pp. 1236–1242.   
[39] G. Zhang and Y. Shi, “Hybrid sampling evolution strategy for solving single objective bound constrained problems,” in 2018 IEEE Congress on Evolutionary Computation (CEC). IEEE, 2018, pp. 1–7.   
[40] J. J. Liang, B. Y. Qu, and P. N. Suganthan, “Problem definitions and evaluation criteria for the CEC 2014 special session and competition on single objective real-parameter numerical optimization,” Computational Intelligence Laboratory, Zhengzhou University, Zhengzhou China and Technical Report, Nanyang Technological University, Singapore, vol. 635, no. 2, p. 2014, 2013.   
[41] S. Jiang, S. Yang, X. Yao, K. C. Tan, M. Kaiser, and N. Krasnogor, “Benchmark functions for the cec’2018 competition on dynamic multiobjective optimization,” Newcastle University, Tech. Rep., 2018.   
[42] P. Bujok and P. Kolenovsky, “Eigen Crossover in Cooperative Model of Evolutionary Algorithms Applied to CEC 2022 Single Objective Numerical Optimisation,” in 2022 IEEE Congress on Evolutionary Computation (CEC). IEEE, 2022, pp. 1–8.   
[43] D. Vermetten, F. Ye, and C. Doerr, “Using Affine Combinations of BBOB Problems for Performance Assessment,” in Proceedings of the Genetic and Evolutionary Computation Conference, 2023, pp. 873–881.   
[44] J. Carrasco, S. Garcı´a, M. Rueda, S. Das, and F. Herrera, “Recent trends in the use of statistical tests for comparing swarm and evolutionary computing algorithms: Practical guidelines and a critical review,” Swarm and Evolutionary Computation, vol. 54, p. 100665, 2020.   
[45] N. Hansen and A. Ostermeier, “Completely derandomized selfadaptation in evolution strategies,” Evolutionary Computation, vol. 9, no. 2, pp. 159–195, 2001.   
[46] A. Gissler, “Evaluation of the impact of various modifications to cma-es that facilitate its theoretical analysis,” in Proceedings of the Companion Conference on Genetic and Evolutionary Computation, 2023, pp. 1603– 1610.   
[47] R. Tanabe and A. Fukunaga, “Success-history based parameter adaptation for differential evolution,” in 2013 IEEE Congress on Evolutionary Computation (CEC). IEEE, 2013, pp. 71–78.   
[48] ´O. Espinoza, K. Rodrı´guez-V´azquez, C. I. Hern´andez, and S. RodriguezRomo, “Comparison Of Three Versions Of Whale Optimization Algorithm (WOA) On The Bbob Test Suite,” in Proceedings of the Companion Conference on Genetic and Evolutionary Computation, 2023, pp. 1595–1602.   
[49] J.-Y. Li, K.-J. Du, Z.-H. Zhan, H. Wang, and J. Zhang, “Distributed Differential Evolution With Adaptive Resource Allocation,” IEEE Transactions on Cybernetics, vol. 53, no. 5, pp. 2791–2804, 2023.   
[50] X. Xia, L. Gui, F. Yu, H. Wu, B. Wei, Y.-L. Zhang, and Z.-H. Zhan, “Triple Archives Particle Swarm Optimization,” IEEE Transactions on Cybernetics, vol. 50, no. 12, pp. 4862–4875, 2020.   
[51] B. Morales-Castan˜eda, D. Zaldivar, E. Cuevas, F. Fausto, and A. Rodrı´guez, “A better balance in metaheuristic algorithms: Does it exist?” Swarm and Evolutionary Computation, vol. 54, p. 100671, 2020.

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/27099197719e3c863640b2e18d10eff48792c2513ad7a2b0490d6b9f27c54265.jpg)

Yaotong Song received the B.S. degree from Beijing University of Civil Engineering and Architecture, Beijing, China. He is currently pursuing the M.E. degree from the University of Toyama, Toyama, Japan. His current interests include computational intelligence and neural networks for real-world applications.

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/8816fd4049b42bd5669eec6a1174540c6964d17e159da808068a6da8b4f7a180.jpg)

Kaiyu Wang received the B.S. degree from Southwest University, Chongqing, China, in 2018, and the M.E. degree from the University of Toyama, Toyama, Japan, in 2022, where he is currently pursuing the Ph.D. degree. His current interests include computational intelligence and neural networks for real-world applications.

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/317d89eddc154ff9ac4e7b47f2f9f7a55029d555ab4d67d100c05da3700246b7.jpg)

Zhi-Hui Zhan (Fellow, IEEE) received the Bachelor’s degree and the Ph. D. degree in Computer Science from the Sun Yat-Sen University, Guangzhou China, in 2013. He is currently a Changjiang Scholar Young Professor and Gifted Professor with the College of Artificial Intelligence, Nankai University, Tianjin, China. His current research interests include evolutionary computation, swarm intelligence, and their applications in real-world problems. He is currently an Associate Editor of the IEEE Transactions on Evolutionary Computation.

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/dd6baac544f6901d30b989bdcd7b7c711a2d4e13c29a726eee07a82486fbf9ad.jpg)

Zhenyu Lei received the Ph.D. degree in Science and Engineering from the University of Toyama, Toyama, Japan, in 2023. He is currently an Assistant Professor with the Faculty of Engineering, University of Toyama, Japan. His current research interests include evolutionary computation, machine learning, and neural network for real-world applications and optimization problems.

![](https://cdn-mineru.openxlab.org.cn/extract/bba1da17-f074-4fcb-a6a7-c07e3b0547c1/403407f2e2452d41d4971d721695f20a3481dde76ecd55abdaa3ec9d018d8b64.jpg)

Shangce Gao (Senior Member, IEEE) received his Ph.D. degree in Innovative Life Science from University of Toyama, Toyama, Japan in 2011. He is currently a Professor with the Faculty of Engineering, University of Toyama, Japan. His current research interests include nature-inspired technologies, machine learning, and neural networks for realworld applications. He serves as an Associate Editor for many international journals such as IEEE Transactions on Neural Networks and Learning Systems, and IEEE/CAA Journal of Automatica Sinica.