import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# ========== 配置区域 ==========
algorithms = {
    "AlgorithmA": "D:\\shuo\\1_code\DLES\\results_fbo_20_2_mt\\10",  # 替换成你的真实路径
    "AlgorithmB": "D:\\shuo\\实验结果\\FMTBO\\FMTBO_SMOP_NIID2",
    "AlgorithmC": "D:\\shuo\\实验结果\\FDEMD\\FDEMD_MTOP_NIID2",
}

save_dir = "./figer"
os.makedirs(save_dir, exist_ok=True)

task_name = "Task in SMOP1"
num_clients = 18  # 每列对应一个客户端（任务）
colors = ['tab:blue', 'orange', 'green', 'crimson', 'purple', 'gray', 'brown', 'cyan']
markers = ['v', 'D', 's', 'p', 'x', '^', '*', 'o']

# ========== 工具函数 ==========
def make_monotonic(y):
    for i in range(1, len(y)):
        y[i] = min(y[i], y[i - 1])
    return y

# ========== 主绘图流程 ==========
for client_id in range(num_clients):
    plt.figure(figsize=(5, 5))

    for algo_idx, (algo_name, algo_path) in enumerate(algorithms.items()):
        run_files = sorted([f for f in os.listdir(algo_path) if f.endswith('.csv')])
        all_runs = []

        for f in run_files:
            df = pd.read_csv(os.path.join(algo_path, f))
            values = df.iloc[:, client_id].values  # 第 client_id 列
            all_runs.append(values)

        data = np.stack(all_runs, axis=0)
        mean = np.mean(data, axis=0)
        std = np.std(data, axis=0)
        mean = make_monotonic(mean)

        rounds = np.arange(len(mean))
        color = colors[algo_idx % len(colors)]
        marker = markers[algo_idx % len(markers)]
        plt.plot(rounds, mean, label=algo_name, color=color, marker=marker)
        plt.fill_between(rounds, mean - std, mean + std, color=color, alpha=0.2)

    plt.xlabel("Communication Round")
    plt.ylabel("Fitness Value")
    plt.title(f"(Client {client_id}) {task_name}")
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f"client{client_id}_task.png"), dpi=300)
    plt.close()

print(f"✅ 图像已生成，共 {num_clients} 张，保存在 {save_dir}/")
