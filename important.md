# 2.论文撰写

## 方案部分书写:

1.一致性原则
数学符号、数学公式、专有名词、图标格式前后表达一定要一致。
2.所有出现的数学符号第一次出现必须定义。
3.整体框架、远景、示意图非常重要,
4.建议使用Latex书写
5.图标分表率要够，清晰是基本要求

## 主要创新点总结

### 1. 首次将 DLES 算法扩展到联邦多任务优化场景

- 实现了隐私保护下的深度学习增强优化
- 保持了 DLES 算法的三阶段优化优势

### 2. 提出轨迹相似性驱动的个性化聚合策略

- 基于客户端优化轨迹的改进率序列计算相似性
- 使用皮尔逊相关系数度量任务间相似性
- 实现更精准的知识共享

### 3. 设计完整的三阶段联邦优化框架

- **阶段 1：分布式探索** - 客户端独立进行本地探索采样
- **阶段 2：个性化聚合** - 基于轨迹相似性的智能模型聚合
- **阶段 3：联邦开发** - 使用个性化全局模型进行最终优化

### 4. 实现真正的多任务联邦优化

- 每个客户端优化不同的任务（18 个基准任务）
- 不是简单的数据分割，而是任务级别的联邦优化
- 支持 Non-IID 数据分布

### 5. 技术创新点

- **神经网络架构**：1-2D-4D-D 的紧凑设计
- **损失函数**：适应度均方误差（FMSE）损失
- **聚合策略**：Top-K 相似客户端选择机制
- **隐私保护**：仅共享模型参数，不泄露原始数据

### 6. 实验验证

- 在 18 个不同类型的基准任务上验证
- 与 FMTBO、FD-EMD 等最新方法对比
- 全面的消融实验和参数敏感性分析

---

## 写作重点

### 重点强调的内容

1. **轨迹相似性聚合的创新性** - 这是与现有方法的主要区别
2. **DLES 在联邦环境中的优势** - 深度学习增强的搜索路径重构
3. **隐私保护与性能的平衡** - 实用性考虑
4. **实验结果的全面性** - 18 个任务的广泛验证

## Introduction书写

### 一个好的introduction必须交代清楚

在写introduction前，先明确自己论文的主要贡献点(创新点)有哪些? **--主题目标**
要努力提炼、浓缩贡献点，并使用1、2、3罗列出来，然后甄别出你论文的全世界独一无二的创新点(敢使用for the first time来套用的点)这就是你心目中的那个要去的“罗马”，也就是你最终落笔的地方。

行文思路 --路线
到底采用什么逻辑线走向这个罗马，现实中你可能会浮现出很多条思路来。这些思路各有优缺点，总体要以“利于非同行，
理解”为原则进行，不要将读者人评审人置于和你同一条起跑线上。评审人其实很想知道你想做什么但是又缺乏必要的基础，这个时候需要你一步一步地引导。

形象一点，Introduction就像一个漏斗:也即是一个范围逐步缩小的过程从社会与科学界逐渐缩小到本文所要研究的topic上去，缩小到你的独一无二贡献点上去(这才显得你的研究有独特的价值)

- background(背景)
- motivation(研究动机)
- research question(要研究问题)
- challenge(问题的挑战性)
- state-of-art(研究现状)
- the unique novelty(你的论文独一无二的贡献)



背景与意义：XXX非常重要，引起广泛兴趣，得到或者有潜力得到广应用。

具体说出研究YYY题目的重要性考虑YYY会怎么样:如果不考虑会怎样。
自己idea。不同以往的做法，本文提出了一种ZZZ,可以克服目前大多数方面的某某缺点，具有...的优势然后列举本文贡献点和创新点。

引出话题。在XXX的研究或者应用中YYY不可避免的会遇到。或者:YYY是核心问题之一，必要给出YYY的定头。
State-of-art。即国内外研究现状。具体地分析、解释、概括国际、国内学老关于YYY方面的研究进展。尽量介绍近三年的研究。描述有逻辑性，解释清楚各自优缺点，总结目前研究的不足，引出自己的研究。



### Introduction书写:注意的几个点

1. Literature Review: 逻辑清楚，不要文献堆砌。对待别人的工个要肯定、但是也要小心的使用“however”指出需要提升、改善启发人的地方。
2. 讲述自己的工作:既要描述现象，又要深入本质，更要说出不同与优势。
   在介绍完别人的工作后，并采用受此启发，或者说however后，我们要开始鼓吹自己的工作了。那么我们这个工作是受什么启发的呢?一般是我们观察到了某一个现象，这个现象引发了我们深思(记住，要深思，深入深入再深入)。整个逐层深入的过程，其实就是从浅入深地讲述自己设计方案的过程。
3. 以读者为中心。
   很多作者有了先验知识，所以常常会默认读者也站在一样的起跑线上。其实读者根本不知道你要干嘛，也不知道你讲这个有什么用。读者只懂得日常的常识和感受，即现象。所以，应该先写现象，然后再慢慢展开，读者就更能够理解你想要说什么，你是在思考什么，你为什么这么思考，然后才能了解你的思想脉络。
4. **生动的例子打动人。**
   例子并非越多越好。因为篇幅有限，我们选取的例子，最好是能够一下子点到读者，触发他们的情景联想，同时又比较简短几句话能概括的。另外，在举例子的时候，为了防止自己写偏最好能够在写之前先问自己，这个例子是要体现什么，说明什么然后抓住这个中心，去想例子，才更有力更精准。并且，这个例子最好能够承接上文的同时，引起下文。当然这样的例子可遇不可求。
5. 引用
   (1)不能绕开的经典论文必须引用
   (2)文献越新越好，多引用近三年论文

