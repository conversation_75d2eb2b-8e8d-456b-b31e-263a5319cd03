import pandas as pd
import os

def convert_excel_to_csv(excel_file_path):
    """
    将Excel文件中的所有工作表转换为单独的CSV文件
    
    参数:
    excel_file_path (str): Excel文件路径
    """
    try:
        # 读取Excel文件
        xl_file = pd.ExcelFile(excel_file_path)
        
        print(f"正在处理文件: {excel_file_path}")
        print(f"发现 {len(xl_file.sheet_names)} 个工作表: {xl_file.sheet_names}")
        
        # 创建输出目录
        base_name = os.path.splitext(excel_file_path)[0]
        output_dir = f"{base_name}_csv"
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        
        # 转换每个工作表
        for sheet_name in xl_file.sheet_names:
            print(f"\n正在处理工作表: {sheet_name}")
            
            # 读取工作表数据
            df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
            
            # 生成CSV文件名
            csv_filename = f"{sheet_name}.csv"
            csv_path = os.path.join(output_dir, csv_filename)
            
            # 保存为CSV
            df.to_csv(csv_path, index=False, encoding='utf-8')
            
            print(f"  - 数据形状: {df.shape}")
            print(f"  - 保存到: {csv_path}")
        
        print(f"\n✅ 转换完成！所有CSV文件已保存到: {output_dir}")
        
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {str(e)}")

if __name__ == "__main__":
    # Excel文件路径
    excel_file = "FMTBOE_results copy.xlsx"
    
    # 检查文件是否存在
    if os.path.exists(excel_file):
        convert_excel_to_csv(excel_file)
    else:
        print(f"❌ 文件不存在: {excel_file}")
